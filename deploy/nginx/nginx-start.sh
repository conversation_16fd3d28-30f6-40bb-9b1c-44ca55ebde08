#!/bin/sh
# vim:sw=4:ts=4:et

location=/usr/share/nginx/html

echo "Replacing VAR prefixed environment variables declarations in all frontend built files at $location"
env | while read line; do
  key=$(echo $line | cut -d= -f1)
  value=$(echo $line | cut -d= -f2)
  if [[ "VAR" == $(echo $key | cut -d_ -f1) ]];
  then
    echo "change $key, value $value"
    find $location -type f -exec sed -i "s|$key|$value|g" {} +
  else
    echo "skip $key"
  fi
done

timestamp=$(date +%s)
jsLocation="${location}/static/js"
echo "Renaming $jsLocation/main.$timestamp.js.* files to force download on local cache"

for full_file_name in $jsLocation/*.*
do
    filename=$(basename -- "$full_file_name")
    suffix=${filename:5:8}
    extension="${filename##*.}"
    filename="${filename%.*}"
    newFilename=${filename//$suffix/$timestamp}
    echo "rename from: $filename.$extension to: $newFilename.$extension"
    mv $full_file_name "$jsLocation/$newFilename.$extension"
done

echo "Replacing $location/index.html to new main.$timestamp.js to force download on local cache"
find $location/index.html -type f -exec sed -i "s|$suffix|$timestamp|g" {} +

nginx -g 'daemon off;'
