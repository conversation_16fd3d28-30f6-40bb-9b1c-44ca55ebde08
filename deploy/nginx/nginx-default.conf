add_header X-Frame-Options SAMEORIGIN always;
add_header "X-XSS-Protection" "1; mode=block";
add_header X-Content-Type-Options "nosniff";

server {
  listen 80;

  root   /usr/share/nginx/html;
  index index.html;

  location / {
    try_files $uri $uri/ /index.html;
    client_max_body_size 100M;
    if ($request_uri ~* ".(json|woff2|ico|css|js|gif|jpe?g|png)$") {
      expires 5d;
      access_log off;
      add_header Pragma public;
      add_header Cache-Control "public";
      break;
    }
  }
}
