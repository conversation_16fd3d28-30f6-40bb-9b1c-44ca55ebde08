import { ButtonV2, config } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { CheckLgIcon } from '@ghq-abi/design-system-icons';
import { message } from 'antd';
import { useState } from 'react';
import { TargetDocumentBlockerPopConfirm } from '../TargetDocumentBlocker/TargetDocumentBlockerPopConfirm';
import cascadingService from '../../services/cascading/cascadingService';

export const CascadingApproveButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (newTargetDocumentData: {
    str_status: string;
    str_status_name: string;
    str_reject_reason: string | null;
    bol_can_edit: number;
  }) => void;
}) => {
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleApprove = () => {
    setLoading(true);
    cascadingService
      .approveTargetDocument(targetDocument.uid)
      .then(res => {
        if (res.success) {
          message.success(
            { content: t('message_target_document_approved'), key: 'update' },
            1,
          );
          onUpdate({
            str_status: 'CASCADING_COMPLETED',
            str_status_name: 'Cascading - Completed',
            str_reject_reason: null,
            bol_can_edit: 0,
          });
        } else {
          message.error({ content: res.errorMessage, key: 'update' }, 1);
        }
      })
      .finally(() => setLoading(false));
  };

  return (
    <TargetDocumentBlockerPopConfirm
      targetDocument={targetDocument}
      title={t('message_approval_target_document')}
      onConfirm={handleApprove}
      okText={t('common_yes')}
      cancelText={t('common_no')}
    >
      <ButtonV2
        size="md"
        loading={loading}
        style={{
          backgroundColor: '#44AC21',
          color: config.theme.colors.white,
        }}
        leftIcon={<CheckLgIcon />}
      >
        {t('common_approve')}
      </ButtonV2>
    </TargetDocumentBlockerPopConfirm>
  );
};
