import React, { useEffect, useMemo } from 'react';

import { AgGridReact } from 'ag-grid-react'; // AG Grid component for React rendering
import { GridOptions } from 'ag-grid-community';

type CustomGridProps = {
  height?: number;
  width?: number;
} & GridOptions;

// To learn more about AG Grid and its configuration, see the following link:
// https://www.ag-grid.com/react-data-grid/getting-started/

export const CustomGrid: React.FC<CustomGridProps> = ({
  height,
  width,
  ...options
}) => {
  /**
   * Example of columns definition:
   * [
   *  {
   *    headerName: 'Target Document ID',
   *    field: 'uid_target_document',
   *  },
   *  {
   *    headerName: 'Employee Global ID',
   *    field: 'int_employeeglobalid',
   *    enableRowGroup: true,
   *  },
   *  {
   *    headerName: 'Employee name',
   *    field: 'str_employee_name',
   *    editable: true,
   *  },
   * ]
   */

  // DefaultColDef sets props common to all Columns
  const _defaultColDef = useMemo(
    () => ({
      flex: 1,
      minWidth: 150,
      resizable: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      ...options.defaultColDef,
    }),
    [options.defaultColDef],
  );

  /**
   * Useful properties:
   *
   * processDataFromClipboard
   * ------------------------
   *
   * This event can be used to validate or append data before actually pasting
   * and updating the cell values. It can also be used to show a confirmation
   * or information modal to the user, though, in this case, grid data should
   * be updated via AG Grid's Transaction Update API.
   */

  useEffect(
    () =>
      // Loop through each .grid element
      document.querySelectorAll('.ag-grid-wrapper').forEach(gridElement => {
        if (gridElement.parentElement) {
          /* eslint-disable no-param-reassign */
          gridElement.parentElement.style.height = '100%';
        }
      }),
    [],
  );

  // On the div wrapping Grid
  // a) Specify theme CSS Class Class and
  // b) Set Grid size
  return (
    <div
      className="ag-theme-alpine ag-grid-wrapper"
      style={{
        width: width || '100%',
        height: height || '100%',
        padding: 10,
        backgroundColor: '#fff',
        borderRadius: 4,
      }}
    >
      <AgGridReact
        // Set to true to block cut operations within the grid.
        suppressCutToClipboard
        // Set to true to work around a bug with Excel (Windows) that adds an
        // extra empty line at the end of ranges copied to the clipboard.
        suppressLastEmptyLineOnPaste
        // Set to true to suppress column moving, i.e. to make the columns
        // fixed position.
        suppressMovableColumns
        // Set to true to enable Row Animation.
        animateRows
        enableRangeSelection
        rowSelection="multiple"
        // Default Column Properties
        defaultColDef={_defaultColDef}
        // pagination
        {...options}
      />
    </div>
  );
};
