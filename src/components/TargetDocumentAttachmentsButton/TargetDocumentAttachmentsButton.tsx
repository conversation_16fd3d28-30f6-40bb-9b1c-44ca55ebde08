import { IconButtonV2, Text, Tooltip } from '@ghq-abi/design-system';
import { PaperClipIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { useState } from 'react';
import adminService from '../../services/admin/adminService';
import { ModalTargetDocumentAttachment } from '../modals/modalTargetDocumentAttachment';

export const TargetDocumentAttachmentsButton = ({
  targetDocument,
  canAdd = true,
  canDelete = true,
}: {
  targetDocument: {
    uid: string;
    str_status: string;
    bol_pre_assignation_book_owner?: boolean;
  };
  canAdd?: boolean;
  canDelete?: boolean;
}) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<any[]>([]);
  const [showAttachmentsModal, setShowAttachmentsModal] = useState(false);

  const handleOpenAttachmentsModal = () => {
    setShowAttachmentsModal(true);
    adminService.listAttachmentTargetDocument(targetDocument.uid).then(res => {
      if (res.success) {
        setFiles(res.data);
      } else {
        message.error(res.errorMessage, 1);
      }
    });
  };

  return (
    <>
      <Tooltip
        content={
          (
            <Text
              css={{
                color: '$white',
                fontSize: '0.75rem',
                fontWeight: '$bold',
              }}
              dangerouslySetInnerHTML={{
                __html: t('common_attachments'),
              }}
            />
          ) as any
        }
        side="top"
      >
        <IconButtonV2
          size="md"
          icon={<PaperClipIcon />}
          variant="ghost"
          onClick={handleOpenAttachmentsModal}
          rounded
          showActiveOutline={false}
        />
      </Tooltip>

      <ModalTargetDocumentAttachment
        targetDocumentUid={targetDocument.uid}
        targetDocumentStatus={targetDocument.str_status}
        files={files}
        isVisible={showAttachmentsModal}
        handleChangeFiles={newFiles => setFiles(newFiles)}
        handleCancel={() => setShowAttachmentsModal(false)}
        disableBlockRemove={!canDelete}
        disableAddRemoveItems={!canAdd}
        askForSharedTargetCascading={
          targetDocument.str_status.startsWith('APPRAISAL') &&
          targetDocument.str_status !== 'APPRAISAL_COMPLETE' &&
          targetDocument.bol_pre_assignation_book_owner
        }
      />
    </>
  );
};
