import { ButtonV2 } from '@ghq-abi/design-system';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowRepeatIcon } from '@ghq-abi/design-system-icons';
import appraisalService from '../../services/appraisal/appraisalService';
import { TargetDocumentBlockerPopConfirm } from '../TargetDocumentBlocker/TargetDocumentBlockerPopConfirm';
import { IRejectEditTargetDocumentReturn } from '../../services/appraisal/types';

export const AppraisalRejectWithChangesButton = <
  T extends {
    uid: string;
    str_status: string;
    str_status_name?: string;
    bol_can_edit: number;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
    obj_target_document_kpis: Array<{
      flt_final_appraisal?: number | null;
      str_final_value?: string | null;
    }>;
  },
>({
  body,
  onUpdate,
  targetDocument,
}: {
  body: Array<{
    uid_target_document: string;
    uid_target_document_kpi: string;
    flt_final_appraisal?: number | null;
    str_final_value?: string | null;
  }>;
  onUpdate: (res: IRejectEditTargetDocumentReturn) => void;
  targetDocument: T;
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const onClick = () => {
    if (!body.length) return;
    setLoading(true);
    appraisalService
      .rejectEditTargetDocument(body)
      .then(res => onUpdate(res))
      .finally(() => setLoading(false));
  };

  return (
    <TargetDocumentBlockerPopConfirm
      targetDocument={targetDocument}
      title={t('message_reject_target_document')}
      onConfirm={onClick}
      okText={t('common_yes')}
      cancelText={t('common_no')}
    >
      <ButtonV2
        size="md"
        variant="secondary"
        loading={loading}
        style={{
          borderColor: '#44AC21',
          color: '#44AC21',
        }}
        leftIcon={<ArrowRepeatIcon width={20} height={20} />}
      >
        {t('common_reject_with_changes')}
      </ButtonV2>
    </TargetDocumentBlockerPopConfirm>
  );
};
