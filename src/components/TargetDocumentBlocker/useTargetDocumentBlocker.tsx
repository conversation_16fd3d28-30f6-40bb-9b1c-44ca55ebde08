import { PropsWithChildren, createContext, useContext } from 'react';

import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';

export type TargetDocumentWithMYRReference = {
  csv_myr_requests_to_freeze_kpis?: number | string;
  int_mid_year_review_request_id?: number;
};

const getIsBlockedByTargetDocument = (
  targetDocument?: TargetDocumentWithMYRReference,
) =>
  !!targetDocument?.csv_myr_requests_to_freeze_kpis ||
  !!targetDocument?.int_mid_year_review_request_id;

const TargetDocumentBlockerContext = createContext(
  {} as { targetDocument?: TargetDocumentWithMYRReference; useToast?: boolean },
);

const useOpenTargetDocumentBlockWarning = () => {
  const { t } = useTranslation();

  const openWarning = (
    targetDocument?: TargetDocumentWithMYRReference,
    useToast?: boolean,
  ) => {
    const isBlocked = getIsBlockedByTargetDocument(targetDocument);
    if (isBlocked) {
      if (useToast) {
        toast.error(t('myr_block_target_document_message'));
      } else {
        message.error(t('myr_block_target_document_message'));
      }
      return true;
    }
    return false;
  };

  return {
    openWarning,
  };
};

/**
 * This component was created to store the state of the current Target Document in the context.
 * This is necessary to allows blocking actions in the children.
 *
 * The target document blocking is necessary because it could be under a Mid Year Review Request.
 * So it will be unblocked when the MYR Request is concluded.
 */
export const TargetDocumentBlockerProvider: React.FC<
  PropsWithChildren<{
    /** Target Document Object. */
    targetDocument?: TargetDocumentWithMYRReference;
    /** If the warning message will use reat-toastfy or not. Default is false. */
    useToast?: boolean;
  }>
> = ({ children, useToast, targetDocument }) => {
  return (
    <TargetDocumentBlockerContext.Provider
      // eslint-disable-next-line react/jsx-no-constructed-context-values
      value={{
        targetDocument,
        useToast,
      }}
    >
      {children}
    </TargetDocumentBlockerContext.Provider>
  );
};

/**
 * This hook can be used with or without a TargetDocumentBlockerProvider.
 * In case that you choose to not use the provider you must pass the target document as argument.
 *
 * Business Explanation: The target document blocking is necessary because it could be under a Mid Year Review Request.
 * So it will be unblocked when the MYR Request is concluded.
 */
export const useTargetDocumentBlocker = (
  targetDocument?: TargetDocumentWithMYRReference,
  useToast?: boolean,
) => {
  const context = useContext(TargetDocumentBlockerContext);
  const { openWarning } = useOpenTargetDocumentBlockWarning();

  return {
    isBlocked: getIsBlockedByTargetDocument(
      targetDocument || context.targetDocument,
    ),
    openWarning: (targetDocumentObj?: TargetDocumentWithMYRReference) =>
      openWarning(
        targetDocumentObj || targetDocument || context.targetDocument,
        useToast || context.useToast,
      ),
  };
};
