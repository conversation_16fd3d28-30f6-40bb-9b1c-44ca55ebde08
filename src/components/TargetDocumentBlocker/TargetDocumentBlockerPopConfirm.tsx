import { Popconfirm, PopconfirmProps } from 'antd';
import React, { ReactElement, cloneElement } from 'react';
import {
  TargetDocumentWithMYRReference,
  useTargetDocumentBlocker,
} from './useTargetDocumentBlocker';

/**
 * This component was created to block Popconfirm buttons when the Target Document is under a MYR Request.
 */
export const TargetDocumentBlockerPopConfirm: React.FC<
  PopconfirmProps & {
    targetDocument?: TargetDocumentWithMYRReference;
  }
> = ({ children, targetDocument, ...rest }) => {
  const { isBlocked, openWarning } = useTargetDocumentBlocker(targetDocument);

  const modifiedChildren = React.Children.map(children, child => {
    if (React.isValidElement(child) && isBlocked) {
      return cloneElement(child, {
        ...child.props,
        onClick: () => openWarning(),
      });
    }
    return child;
  });

  if (isBlocked && modifiedChildren) {
    return modifiedChildren as unknown as ReactElement;
  }
  return <Popconfirm {...rest}>{children}</Popconfirm>;
};
