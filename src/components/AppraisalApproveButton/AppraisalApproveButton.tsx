import { ButtonV2, config } from '@ghq-abi/design-system';
import { CheckLgIcon } from '@ghq-abi/design-system-icons';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import appraisalService from '../../services/appraisal/appraisalService';
import { TargetDocumentBlockerPopConfirm } from '../TargetDocumentBlocker/TargetDocumentBlockerPopConfirm';

export const AppraisalApproveButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (newTargetDocumentData: {
    str_status: string;
    str_status_name: string;
    bol_can_edit: number;
  }) => void;
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleEmployeeApprove = () => {
    setLoading(true);
    appraisalService
      .approveTargetDocument(targetDocument.uid)
      .then(res => {
        if (res.success) {
          message.success({ content: t('message_target_document_approved') });
          onUpdate({
            str_status: 'APPRAISAL_APPRAISAL_COMPLETE',
            str_status_name: t('appraisal_appraisal_complete'),
            bol_can_edit: 0,
          });
        } else {
          message.error({ content: res.errorMessage, key: 'update' }, 1);
        }
      })
      .finally(() => setLoading(false));
  };

  return (
    <TargetDocumentBlockerPopConfirm
      targetDocument={targetDocument}
      title={t('message_approval_target_document')}
      onConfirm={handleEmployeeApprove}
      okText={t('common_yes')}
      cancelText={t('common_no')}
    >
      <ButtonV2
        size="md"
        loading={loading}
        style={{
          backgroundColor: '#44AC21',
          color: config.theme.colors.white,
        }}
        leftIcon={<CheckLgIcon />}
      >
        {t('common_approve_appraisal')}
      </ButtonV2>
    </TargetDocumentBlockerPopConfirm>
  );
};
