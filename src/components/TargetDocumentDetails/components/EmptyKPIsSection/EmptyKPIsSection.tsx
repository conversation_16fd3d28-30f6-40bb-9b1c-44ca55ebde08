import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { ButtonV2, Flex, Text } from '@ghq-abi/design-system';
import { NoDataCircleIcon, PlusLgIcon } from '@ghq-abi/design-system-icons';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';
import { KPIAssignModal } from '../../../KPIAssignModal/KPIAssignModal';

export const EmptyKPIsSection = ({ full_scope }: { full_scope?: boolean }) => {
  const { t } = useTranslation();
  const { setKpis, setEditMode, targetDocument, canAddKPIs } =
    useTargetDocumentDetails();

  const [modalVisible, setModalVisible] = useState(false);

  return (
    <Flex
      direction="column"
      gap="md"
      align="center"
      justify="center"
      css={{ height: '100%' }}
    >
      <NoDataCircleIcon width={64} height={64} fill="#000" />
      <Flex direction="column" gap="sm" align="center">
        <Text color="#191f2e" css={{ fontSize: '1rem', fontWeight: '$medium' }}>
          {t('message_target_this_person_yet')}
        </Text>
        {canAddKPIs && (
          <Text
            color="#7d8597"
            css={{ fontSize: '0.75rem', fontWeight: '$light' }}
          >
            {t('message_target_add_this_employee')}
          </Text>
        )}
      </Flex>
      {canAddKPIs && (
        <ButtonV2
          size="md"
          variant="primary"
          leftIcon={<PlusLgIcon />}
          onClick={() => setModalVisible(true)}
        >
          {t('common_add_targets')}
        </ButtonV2>
      )}
      <KPIAssignModal
        kpisYear={targetDocument.dat_finish?.slice(0, 4)}
        selectMultiple
        open={modalVisible}
        full_scope={full_scope}
        onClose={() => setModalVisible(false)}
        onSelectMultiple={selecteds => {
          const newKpis = selecteds.map(item => ({
            ...item,
            str_name: item.str_kpi_name,
          }));
          setKpis(newKpis);
          setEditMode(true);
        }}
      />
    </Flex>
  );
};
