import { Box, Flex, Tooltip } from '@ghq-abi/design-system';
import { InfoCircleFillIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';
import { isNull } from 'lodash';
import { PerformanceEntityContent } from '../../../PerformanceEntityContent';
import { TextLabel } from '../../../TextLabel/TextLabel';
import { TextDescription } from '../../../TextDescription/TextDescription';
import configs from '../../../../services/configs';
import { Avatar } from '../../../avatar';
import {
  STATUS_CAPTION_ICON,
  STATUS_NAME,
} from '../../../TargetDocumentList/constants';
import { FeedbackReasonTooltip } from './FeedbackReasonTooltip';
import { SharedTargetIcon } from '../../../TargetDocumentList/icons';
import { useTargetDocumentSelection } from '../../../../hooks/useTargetDocumentSelection';

export const EmployeeHeader = () => {
  const { t } = useTranslation();
  const { selected } = useTargetDocumentSelection();

  const contentTooltipEntity = (): any => {
    return (
      <PerformanceEntityContent
        performanceEntity={selected!.performance_entity!}
      />
    );
  };

  const contentTooltipInfo = (): any => {
    return (
      <Flex gap="md">
        <Box css={{ display: 'flex', flexDirection: 'column' }}>
          <TextLabel style={{ color: 'white' }}>
            {t('common_time_dedication')}
          </TextLabel>
          <TextDescription>{selected?.int_time_dedication}%</TextDescription>
        </Box>
        <Box css={{ display: 'flex', flexDirection: 'column' }}>
          <TextLabel style={{ color: 'white' }}>
            {t('common_bonus_schema')}
          </TextLabel>
          <TextDescription>{selected?.str_bonus_schema_name}</TextDescription>
        </Box>
      </Flex>
    );
  };

  const avatarInfo = {
    name: selected?.str_employee_name,
    photoUrl:
      configs.photo_api_url && selected?.int_employee_global_id
        ? `${configs.photo_api_url}/${selected?.int_employee_global_id}`
        : undefined,
  };

  return (
    (selected && avatarInfo.name && (
      <Flex gap="md" justify="between">
        <Flex gap="sm" align="center">
          <Avatar
            size="sm"
            name={avatarInfo.name}
            enablePreLoading
            src={avatarInfo.photoUrl}
            imageCss={{
              backgroundColor: 'transparent',
              backgroundImage: 'none',
            }}
          />

          <Flex direction="column">
            <Tooltip align="center" content={contentTooltipInfo()} side="top">
              <Flex align="center" gap="xs" justify="start">
                {selected.str_employee_name}
                <InfoCircleFillIcon width={14} height={14} />
              </Flex>
            </Tooltip>
            <Flex gap="md">
              <TextDescription>
                {selected.str_employee_positiontitle || ''}
              </TextDescription>
              {!!selected.performance_entity?.length && (
                <Tooltip
                  align="center"
                  content={contentTooltipEntity()}
                  side="right"
                >
                  <TextDescription>
                    {t('common_entity')}:{' '}
                    {selected.performance_entity?.[0]?.str_name}
                  </TextDescription>
                </Tooltip>
              )}
              {selected.str_manager_name && (
                <TextDescription>
                  {t('common_appraiser')}: {selected.str_manager_name}
                </TextDescription>
              )}
            </Flex>
          </Flex>
        </Flex>

        <Flex gap="sm" align="center" direction="column">
          <Flex align="center">
            {(selected.str_status === 'APPRAISAL_EMPLOYEE_IN_PROGRESS' ||
              selected.str_status === 'CASCADING_MANAGER_EDITING') &&
            selected.str_reject_reason ? (
              <FeedbackReasonTooltip reason={selected.str_reject_reason} />
            ) : (
              STATUS_CAPTION_ICON[selected.str_status]
            )}
            <Box css={{ paddingLeft: '5px' }}>{selected.str_status_name}</Box>
          </Flex>

          {!isNull(selected.id_spoc) && (
            <Box
              css={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.3rem',
              }}
            >
              <SharedTargetIcon /> {STATUS_NAME.SHARED_TARGET}
            </Box>
          )}
        </Flex>
      </Flex>
    )) ||
    null
  );
};
