import { Tooltip } from 'antd';
import { ChatAlertIcon } from '@ghq-abi/design-system-icons';
import styled from 'styled-components';

export const FeedbackIconWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const FeedbackReasonTooltip = ({ reason }: { reason: string }) => {
  return (
    <Tooltip placement="topLeft" title={reason}>
      <FeedbackIconWrapper>
        <ChatAlertIcon width={20} height={20} />
      </FeedbackIconWrapper>
    </Tooltip>
  );
};
