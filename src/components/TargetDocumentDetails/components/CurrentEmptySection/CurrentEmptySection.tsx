import { Box, Label, Text } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { CascadingEmployeeIcon } from '../../../icons/CascadingEmployeeIcon';

export const CurrentEmptySection = () => {
  const { t } = useTranslation();

  return (
    <Box
      css={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        textAlign: 'center',
        gap: '16px',
      }}
    >
      <CascadingEmployeeIcon />
      <Label css={{ fontSize: '1rem', fontWeight: '$normal' }}>
        {t('cascading_choose_employee')}
      </Label>
      <Text
        css={{
          fontSize: '0.75rem',
          fontWeight: '$medium',
          color: '#7D8597',
        }}
      >
        {t('message_current_choose_employee')}
      </Text>
    </Box>
  );
};
