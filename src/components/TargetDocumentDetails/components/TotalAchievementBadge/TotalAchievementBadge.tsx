import { Flex } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { TextLabel } from '../../../TextLabel/TextLabel';
import TrafficLightLegendUnique from '../../../trafficLight/TrafficLightLegendUnique';
import { TextDescription } from '../../../TextDescription/TextDescription';
import { calculateTotalAchievementPercentage } from '../../../../utils';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';

export const TotalAchievementBadge = () => {
  const { kpis } = useTargetDocumentDetails();
  const { t } = useTranslation();

  const achievementPercent = calculateTotalAchievementPercentage(kpis || []);

  return (
    <Flex
      align="center"
      gap="sm"
      css={{
        background: '#FBFAFC',
        borderRadius: '200px',
        padding: '1rem',
        height: '40px',
      }}
    >
      <TextLabel>{t('common_total_achievement')}:</TextLabel>
      <Flex gap="sm" align="center">
        <TrafficLightLegendUnique
          value={Number(achievementPercent.split('%')[0])}
        />
        <TextDescription>{achievementPercent}</TextDescription>
      </Flex>
    </Flex>
  );
};
