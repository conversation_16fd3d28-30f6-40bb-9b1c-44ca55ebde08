import { Flex, Label, Text } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { CascadingEmployeeIcon } from '../../../icons/CascadingEmployeeIcon';
import { CascadingKpisIcon } from '../../../icons/CascadingKpisIcon';
import { OpenCatalogButton } from './OpenCatalogButton';

export const CascadingEmptySection = ({
  refresh,
  full_scope,
}: {
  refresh: () => void;
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <Flex direction="column" justify="center" align="center">
      <Flex align="center" gap="xl">
        <Flex
          direction="column"
          align="center"
          gap="md"
          css={{
            width: '290px',
            height: '352px',
            textAlign: 'center',
          }}
        >
          <CascadingEmployeeIcon />
          <Label css={{ fontSize: '1rem', fontWeight: '$normal' }}>
            {t('cascading_choose_employee')}
          </Label>
          <Text
            css={{
              fontSize: '0.75rem',
              fontWeight: '$medium',
              color: '#7D8597',
            }}
          >
            {t('message_cascading_choose_employee')}
          </Text>
        </Flex>
        <Flex
          align="center"
          justify="center"
          css={{
            width: '37px',
            height: '37px',
            backgroundColor: '$white',
            borderRadius: '100px',
            padding: '10px',
            border: '1px solid#EEEFF2',
          }}
        >
          <Text>{t('common_or')}</Text>
        </Flex>
        <Flex
          direction="column"
          align="center"
          justify="between"
          css={{
            width: '290px',
            height: '352px',
          }}
        >
          <Flex
            direction="column"
            align="center"
            justify="center"
            css={{
              textAlign: 'center',
              gap: '16px',
            }}
          >
            <CascadingKpisIcon />
            <Label css={{ fontSize: '1rem', fontWeight: '$normal' }}>
              {t('cascading_select_kpis')}
            </Label>
            <Text
              css={{
                fontSize: '0.75rem',
                fontWeight: '$medium',
                color: '#7D8597',
              }}
            >
              {t('message_cascading_select_kpis')}
            </Text>
          </Flex>
          <OpenCatalogButton refresh={refresh} full_scope={full_scope} />
        </Flex>
      </Flex>
    </Flex>
  );
};
