import { CheckCircleIcon } from '@ghq-abi/design-system-icons';
import { ButtonV2 } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import ModalV2 from '../../../modalV2/ModalV2';

export const SuccessModal = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => Promise<void>;
}) => {
  const { t } = useTranslation();
  return (
    <ModalV2
      open={open}
      width="459px"
      title="Congratulations!"
      hasFooter={false}
      onCancel={onClose}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '32px',
          textAlign: 'center',
          color: '#7D8597',
          fontWeight: 600,
        }}
      >
        <CheckCircleIcon color="#44AC21" width={100} height={100} />
        {t('modal_employee_assign_success_text')}
        <ButtonV2 size="md" onClick={onClose}>
          {t('common_understood')}
        </ButtonV2>
      </div>
    </ModalV2>
  );
};
