import { ButtonV2, Flex } from '@ghq-abi/design-system';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KPIAssignModal } from '../../../KPIAssignModal/KPIAssignModal';
import { SuccessModal } from './SuccessModal';

interface OpenCatalogButtonProps {
  refresh: () => void;
  full_scope?: boolean;
}

export const OpenCatalogButton = ({
  refresh,
  full_scope,
}: OpenCatalogButtonProps) => {
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [successVisible, setSuccessVisible] = useState(false);
  const { t } = useTranslation();

  return (
    <>
      <ButtonV2
        size="md"
        variant="primary"
        onClick={() => {
          setAssignModalVisible(true);
        }}
      >
        <Flex
          css={{
            alignItems: 'center',
          }}
        >
          {t('common_open_catalog')}
        </Flex>
      </ButtonV2>

      <KPIAssignModal
        onSelectMultiple={() => undefined}
        open={assignModalVisible}
        selectMultiple={false}
        onClose={() => setAssignModalVisible(false)}
        onSuccess={() => setSuccessVisible(true)}
        kpisYear={new Date().getFullYear()}
        full_scope={full_scope}
      />
      <SuccessModal
        open={successVisible}
        onClose={async () => {
          await refresh();
          setSuccessVisible(false);
        }}
      />
    </>
  );
};
