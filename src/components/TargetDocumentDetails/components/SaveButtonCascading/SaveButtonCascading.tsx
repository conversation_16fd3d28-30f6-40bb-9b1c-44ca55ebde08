import { ButtonV2 } from '@ghq-abi/design-system';
import { useState } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import targetDocumentService from '../../../../services/targetDocument/targetDocumentService';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';

export const SaveButtonCascading = () => {
  const { kpis, targetDocument, setKpis, setEditMode, onUpdate } =
    useTargetDocumentDetails();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  return (
    <ButtonV2
      variant="primary"
      size="md"
      loading={loading}
      onClick={async () => {
        setLoading(true);
        const result = await targetDocumentService.updateKpi(
          kpis.map(kpi => ({
            uid_kpi: kpi.uid,
            uid_target_document: targetDocument.uid,
            uid_target_document_kpi: kpi.uid_target_document_kpi,
            int_weight: kpi.int_weight,
            str_value: kpi.str_value,
            str_pa_value: kpi.str_pa_value,
            str_scope: kpi.str_scope,
          })),
        );
        setLoading(false);
        if (!result.success) message.error(result.errorMessage);
        else {
          message.success('KPI(s) saved successfully');
          setKpis(result.data!.results);
          onUpdate({
            obj_target_document_kpis: result.data!.results,
          });
          setEditMode(false);
        }
      }}
    >
      {t('common_save_edit')}
    </ButtonV2>
  );
};
