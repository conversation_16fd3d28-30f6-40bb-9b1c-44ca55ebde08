import React from 'react';
import { useTranslation } from 'react-i18next';
import { Tooltip } from 'antd';
import { monthsNames } from '../../../../../utils/monthsNames';
import { TrafficLightTrigger } from './styles';
import { getTrafficLightColor, TrafficLightPercentageMap } from './common';

export const getTrafficLight = (
  value: number | null,
  props = {},
  showWhite = false,
) => (
  <Tooltip title={TrafficLightPercentageMap[value ?? 'empty']}>
    <TrafficLightTrigger
      backgroundColor={getTrafficLightColor(value, showWhite)}
    />
  </Tooltip>
);

export const AnnualView = React.memo(
  ({
    str_name,
    str_kpi_id,
    obj_monitoring,
  }: {
    str_name?: string;
    str_kpi_id?: string;
    obj_monitoring?: MonitoringType[];
  }) => {
    const { t } = useTranslation();
    const dateNow = new Date();
    const currentYear = dateNow.getUTCFullYear();
    const currentMonth = dateNow.getUTCMonth();

    const getMonth = (i: number) => (
      <span
        style={{
          height: '1rem',
          width: '3rem',
          padding: 0,
          fontWeight: '400',
          fontSize: '0.75rem',
          color: '#7D8597',
        }}
      >
        {t(monthsNames[i].short)}
      </span>
    );

    const fillEmptyMonitoring = (monitorings: Array<MonitoringType>) => {
      const fullyEmpty = Array.from<MonitoringType, MonitoringType>(
        { length: 12 },
        (_, k) => ({
          dat_month_year: `${currentYear}-${String(k + 1).padStart(2, '0')}`,
          int_value: null,
          int_value_le: null,
        }),
      );

      if (monitorings) {
        fullyEmpty.forEach((empty, i) => {
          const monitoring = monitorings.find(
            m => m.dat_month_year?.substring(0, 7) === empty.dat_month_year,
          );

          if (monitoring) {
            fullyEmpty[i] = monitoring;
          }
        });
      }

      return fullyEmpty;
    };

    const getTrackTitle = (title: string) => (
      <span
        style={{
          fontSize: '0.75rem',
          fontWeight: '600',
          color: '$white',
          margin: '0.5rem',
        }}
      >
        {title}
      </span>
    );

    const getKpiName = (name: string) => (
      <span
        style={{
          fontSize: '0.75rem',
          fontWeight: '600',
          color: '$black',
        }}
        className="truncate-1"
      >
        {name}
      </span>
    );

    const getKpiId = (id: string) => (
      <span
        style={{
          fontSize: '0.75rem',
          fontWeight: '400',
          color: '#7D8597',
        }}
      >
        {id}
      </span>
    );

    return (
      <div
        style={{
          display: 'grid',
          gridAutoFlow: 'column',
          gridTemplateColumns: '4fr 0.1fr repeat(12, 1fr)',
          justifyContent: 'space-between',
          alignItems: 'center',
          overflowX: 'auto',
          overflowY: 'hidden',
          minHeight: '4.5rem',
          borderRadius: '0.25rem',
          border: '1px solid #e0e0e0',
          padding: '0.5rem',
          paddingRight: '1rem',
          minWidth: '500px',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'flex-start',
            margin: '0.75rem',
            height: '100%',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            minWidth: '100px',
          }}
          title={str_name}
        >
          {str_name && getKpiName(str_name)}
          {str_kpi_id && getKpiId(str_kpi_id)}
        </div>
        <div
          style={{
            marginLeft: '0.75rem',
            borderLeft: '1px solid #e0e0e0',
            height: '100%',
            margin: '0.75rem',
          }}
        />
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            textAlign: 'center',
            justifyContent: 'flex-end',
            alignItems: 'flex-start',
            paddingBottom: '0.25rem',
            height: '100%',
            gap: '0.5rem',
            lineHeight: '0.75rem',
          }}
        >
          <span>{getTrackTitle('YTD')}</span>
          <span>{getTrackTitle('LE')}</span>
        </div>
        {obj_monitoring &&
          fillEmptyMonitoring(obj_monitoring).map((tm, i) => (
            <div
              key={tm.dat_month_year}
              style={{
                display: 'flex',
                flexDirection: 'column',
                textAlign: 'center',
                justifyContent: 'center',
                alignItems: 'center',
                width: '1.25rem',
                height: '100%',
                gap: '0.5rem',
              }}
            >
              {getMonth(i)}
              {getTrafficLight(tm.int_value, {}, i === currentMonth)}
              {getTrafficLight(tm.int_value_le, {}, i === currentMonth)}
            </div>
          ))}
      </div>
    );
  },
);
