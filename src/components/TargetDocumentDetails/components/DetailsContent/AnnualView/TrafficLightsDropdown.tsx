import React, { useRef } from 'react';
import ButtonDropdown from '../../../../buttonDropdownV2/ButtonDropdownV2';
import { TrafficLightTrigger } from './styles';
import { getTrafficLightColor, TrafficLightPercentageMap } from './common';
import { getTrafficLight } from './AnnualView';

export const TrafficLightDropdown = ({
  value,
  onStatusChange,
  isDisabled = false,
  valueType = 'int_value',
  props = {},
}: TrafficLightDropdownProps) => {
  const previousValueRef = useRef(value);

  const handleSelect = e => {
    if (isDisabled) return;
    const selectedValue = parseInt(e.key, 10);
    if (selectedValue !== previousValueRef.current) {
      previousValueRef.current = selectedValue;
      if (onStatusChange) {
        onStatusChange(selectedValue, valueType);
      }
    }
  };

  const trafficLightsRows = [0, 1, 2, 3].map(num => ({
    key: num.toString(),
    label: TrafficLightPercentageMap[num],
    icon: getTrafficLight(num),
  }));

  return (
    <ButtonDropdown
      onClickDropDown={handleSelect}
      rows={trafficLightsRows}
      triggerComponent={
        <TrafficLightTrigger
          backgroundColor={getTrafficLightColor(value)}
          style={props}
          cursor={isDisabled ? 'not-allowed' : 'pointer'}
        />
      }
      disabled={isDisabled}
    />
  );
};
