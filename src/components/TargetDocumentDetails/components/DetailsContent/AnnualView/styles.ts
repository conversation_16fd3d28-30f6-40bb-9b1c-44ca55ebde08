import styled from 'styled-components';

export const TrafficLightTrigger = styled.div<{
  backgroundColor: string;
  style?: React.CSSProperties;
  cursor?: React.CSSProperties['cursor'];
}>`
  background-color: ${({ backgroundColor }) => backgroundColor};
  border: 1px solid #c0c0c0;
  border-radius: 1rem;
  width: 0.75rem;
  height: 0.75rem;
  cursor: ${({ cursor }) => cursor};
  ${({ style }) => style && { ...style }};
`;
