export const TrafficLightColorMap = {
  0: '#FF3236',
  1: '#FFAD0D',
  2: '#F2DC39',
  3: '#44AC21',
  available: '$white', // this exists on the prototype, but not on the old code
  empty: '#CACDD5',
};

export const TrafficLightPercentageMap = {
  0: '0%',
  1: '80%',
  2: '90%',
  3: '100%',
  empty: 'Not Started',
};

export const getTrafficLightColor = (value: number | null, showWhite = false) =>
  (value !== null && TrafficLightColorMap[value]) ||
  (showWhite && TrafficLightColorMap.available) ||
  TrafficLightColorMap.empty;
