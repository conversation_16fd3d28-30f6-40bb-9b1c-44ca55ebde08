import { Accordion } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { AppraisalAccordionItemForm } from '../AppraisalAccordionItemForm/AppraisalAccordionItemForm';
import { StyledTrigger } from './styles';
import { TargetDocumentDetailsKPIType } from '../../../types';
import { TextLabel } from '../../../../TextLabel/TextLabel';
import { TextDescription } from '../../../../TextDescription/TextDescription';

export const AppraisalAccordionItem = ({
  kpi,
  index,
}: {
  kpi: TargetDocumentDetailsKPIType;
  index: number;
}) => {
  const { t } = useTranslation();

  return (
    <Accordion.Item
      key={`item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`}
      value={`item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`}
    >
      <div style={{ display: 'flex', background: 'white' }}>
        <AppraisalAccordionItemForm
          kpi={kpi}
          key={kpi.uid_target_document_kpi}
          index={index}
        />
        <StyledTrigger />
      </div>
      <Accordion.Content>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 3fr',
            gap: '8px',
          }}
        >
          <div>
            <div>
              <TextLabel>{t('common_value')}</TextLabel>
              <TextDescription>{kpi.str_value}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_business_function')}</TextLabel>
              <TextDescription>
                {kpi.str_business_function_name || ''}
              </TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_type')}</TextLabel>
              <TextDescription>{kpi.str_kpi_type}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('common_source')}</TextLabel>
              <TextDescription className="truncate-4">
                {kpi.str_source_name || ''}
              </TextDescription>
            </div>
          </div>
          <div>
            <div>
              <TextLabel>{t('common_pa_value')}</TextLabel>
              <TextDescription>{kpi.str_pa_value}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_calculation_method')}</TextLabel>
              <TextDescription>{kpi.str_calculation_method}</TextDescription>
            </div>
          </div>
        </div>
      </Accordion.Content>
    </Accordion.Item>
  );
};
