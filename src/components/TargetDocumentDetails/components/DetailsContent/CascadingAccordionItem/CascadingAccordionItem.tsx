import { Accordion } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { TargetDocumentDetailsKPIType } from '../../../types';
import { StyledTrigger } from '../AppraisalAccordionItem/styles';
import { CascadingAccordionItemForm } from '../CascadingAccordionItemForm/CascadingAccordionItemForm';
import { TextLabel } from '../../../../TextLabel/TextLabel';
import { TextDescription } from '../../../../TextDescription/TextDescription';

export const CascadingAccordionItem = ({
  kpi,
  index,
  full_scope,
}: {
  kpi: TargetDocumentDetailsKPIType;
  index: number;
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <Accordion.Item
      value={`item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`}
    >
      <div style={{ display: 'flex', background: 'white' }}>
        <CascadingAccordionItemForm
          kpi={kpi}
          index={index}
          key={kpi.uid_target_document_kpi}
          full_scope={full_scope}
        />
        <StyledTrigger />
      </div>
      <Accordion.Content>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 3fr',
            gap: '8px',
          }}
        >
          <div>
            <div>
              <TextLabel>{t('kpi_business_function')}</TextLabel>
              <TextDescription>
                {kpi.str_business_function_name}
              </TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_type')}</TextLabel>
              <TextDescription>{kpi.str_kpi_type}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('common_source')}</TextLabel>
              <TextDescription className="truncate-4">
                {kpi.str_source_name || kpi.str_source}
              </TextDescription>
            </div>
          </div>
          <div>
            <TextLabel>{t('kpi_calculation_method')}</TextLabel>
            <TextDescription>{kpi.str_calculation_method}</TextDescription>
          </div>
        </div>
      </Accordion.Content>
    </Accordion.Item>
  );
};
