import { ButtonV2 } from '@ghq-abi/design-system';
import { TrashAlternativeIcon } from '@ghq-abi/design-system-icons';
import { useState } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import cascadingService from '../../../../../services/cascading/cascadingService';
import { useTargetDocumentBlocker } from '../../../../TargetDocumentBlocker/useTargetDocumentBlocker';

export const KPIDeleteButton = ({
  kpi,
  targetDocument,
  onDelete,
}: {
  kpi: {
    uid_target_document_kpi?: string;
  };
  targetDocument: {
    uid: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onDelete: () => void;
}) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const { openWarning } = useTargetDocumentBlocker(targetDocument);
  const { t } = useTranslation();

  const onDeleteClick = async () => {
    if (kpi.uid_target_document_kpi) {
      setLoadingDelete(true);
      const result = await cascadingService
        .deleteKpiOfTargetDocument({
          uid_target_document_kpi: kpi.uid_target_document_kpi!,
        })
        .finally(() => setLoadingDelete(false));
      if (!result.success) {
        message.error(result.errorMessage);
        return;
      }
    }
    message.success(t('message_kpi_completed_remove'));
    onDelete();
  };

  return (
    <ButtonV2
      size="md"
      variant="borderless"
      loading={loadingDelete}
      onClick={() => {
        if (!openWarning()) onDeleteClick();
      }}
      style={{ gap: '0px' }}
      leftIcon={<TrashAlternativeIcon style={{ display: 'flex' }} />}
    />
  );
};
