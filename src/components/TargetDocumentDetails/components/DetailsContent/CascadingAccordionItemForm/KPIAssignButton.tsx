import { ButtonV2 } from '@ghq-abi/design-system';
import { ArrowFatShareIcon } from '@ghq-abi/design-system-icons';
import { useState } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useTargetDocumentBlocker } from '../../../../TargetDocumentBlocker/useTargetDocumentBlocker';
import { KPIAssignModal } from '../../../../KPIAssignModal/KPIAssignModal';
import { IDataKpi } from '../../../../../services/cascading/types';

export const KPIAssignButton = ({
  targetDocument,
  onUpdate,
  kpi,
  full_scope,
}: {
  targetDocument: {
    uid: string;
    uid_employee: string;
    obj_target_document_kpis: Array<{
      int_weight?: number;
      str_scope?: string;
    }>;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (
    newData: { obj_target_document_kpis: IDataKpi[] },
    targetDocumentUID: string,
  ) => void;
  kpi: { uid_type: string; uid: string };
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();
  const { openWarning } = useTargetDocumentBlocker(targetDocument);
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <>
      <ButtonV2
        size="md"
        variant="borderless"
        onClick={e => {
          e.stopPropagation();
          if (!openWarning()) setModalOpen(true);
        }}
      >
        <ArrowFatShareIcon style={{ display: 'flex' }} />
      </ButtonV2>
      <KPIAssignModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        full_scope={full_scope}
        onSuccess={result => {
          if (result) {
            onUpdate(
              { obj_target_document_kpis: result },
              result[0].uid_target_document,
            );
          }
          message.success(t('message_save'));
        }}
        kpiToAssign={kpi}
      />
    </>
  );
};
