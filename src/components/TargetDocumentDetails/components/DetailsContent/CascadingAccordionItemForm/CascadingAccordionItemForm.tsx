import { Flex, Tooltip, Text } from '@ghq-abi/design-system';
import { Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { TargetDocumentDetailsKPIType } from '../../../types';
import { useTargetDocumentDetails } from '../../../hooks/useTargetDocumentDetails';
import { TextLabel } from '../../../../TextLabel/TextLabel';
import { TextDescription } from '../../../../TextDescription/TextDescription';
import SelectV2 from '../../../../selectV2/SelectV2';
import targetDocumentService from '../../../../../services/targetDocument/targetDocumentService';
import { ValueField } from '../../../../KPIValueField/ValueField';
import { KpiTypeUuids } from '../../../../../utils';
import { KPIDeleteButton } from './KPIDeleteButton';
import { KPIAssignButton } from './KPIAssignButton';
import {
  getPaValueByValueAndKpiType,
  isPAValueDisabled,
} from '../../../../../businessRules';

export const CascadingAccordionItemForm = ({
  kpi,
  index,
  full_scope,
}: {
  kpi: TargetDocumentDetailsKPIType;
  index: number;
  full_scope?: boolean;
}) => {
  const {
    editMode,
    setKpis,
    kpis,
    targetDocument,
    canDeleteKPIs,
    onUpdate: onUpdateTargetDocument,
  } = useTargetDocumentDetails();
  const { t } = useTranslation();

  const onUpdate = (data: Partial<TargetDocumentDetailsKPIType>) => {
    setKpis(
      kpis.map((kpiItem, i) => {
        if (i === index) return { ...kpiItem, ...data };
        return kpiItem;
      }),
    );
  };

  return (
    <div
      style={{
        width: '100%',
        paddingTop: '15px',
        paddingBottom: '15px',
        paddingLeft: '4px',
        paddingRight: '64px',
        display: 'grid',
        height: '92px',
        gap: '10px',
        gridTemplateColumns: '3fr 2fr 2fr 2fr 2fr',
      }}
    >
      <div style={{ marginRight: '20px', marginLeft: '10px' }}>
        <TextLabel className="truncate-2">
          {kpi.str_name || kpi.str_kpi_name || ''}
        </TextLabel>
        <TextDescription>{kpi.str_kpi_id}</TextDescription>
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_weight')}</TextLabel>
        {editMode ? (
          <SelectV2
            showSearch
            placeholder={t('common_weight')}
            onClick={e => e.stopPropagation()}
            options={targetDocumentService.catalogWeightList()}
            value={`${kpi.int_weight || '0'}`}
            onChange={value => onUpdate({ int_weight: +(value as string) })}
          />
        ) : (
          <TextDescription>{`${kpi.int_weight || 0} %`}</TextDescription>
        )}
      </div>
      <div
        style={{
          display: 'grid',
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
        }}
      >
        <TextLabel>{t('common_value')}</TextLabel>
        {editMode ? (
          <ValueField
            onChange={val =>
              onUpdate({
                str_value: `${val}`,
                str_pa_value: getPaValueByValueAndKpiType(
                  `${val}`,
                  kpi.uid_type as KpiTypeUuids,
                  kpi.str_pa_value,
                ),
              })
            }
            value={kpi.str_value}
            selectedKPI={kpi}
            onClick={e => e.stopPropagation()}
          />
        ) : (
          <TextDescription className="truncate-2">
            {kpi.str_value || ''}
          </TextDescription>
        )}
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_pa_value')}</TextLabel>
        {editMode ? (
          <Input
            size="large"
            value={kpi.str_pa_value}
            disabled={isPAValueDisabled(kpi)}
            onClick={e => e.stopPropagation()}
            onChange={e => onUpdate({ str_pa_value: e.target.value })}
          />
        ) : (
          <TextDescription className="truncate-2">
            {kpi.str_pa_value || ''}
          </TextDescription>
        )}
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_scope')}</TextLabel>
        {editMode ? (
          <Input
            size="large"
            value={kpi.str_scope}
            onClick={e => e.stopPropagation()}
            onChange={e => onUpdate({ str_scope: e.target.value })}
          />
        ) : (
          <Tooltip
            content={
              (
                <Text
                  css={{
                    color: '$white',
                    fontSize: '0.75rem',
                    fontWeight: '$bold',
                  }}
                  dangerouslySetInnerHTML={{
                    __html: kpi.str_scope || '',
                  }}
                />
              ) as never
            }
            side="top"
          >
            <TextDescription className="truncate-2">
              {kpi.str_scope || ''}
            </TextDescription>
          </Tooltip>
        )}
      </div>
      <Flex
        style={{ position: 'absolute', right: '8px', top: '40px', zIndex: 999 }}
        align="end"
      >
        <KPIAssignButton
          kpi={kpi}
          full_scope={full_scope}
          targetDocument={targetDocument}
          onUpdate={(data, targetDocumentId) =>
            targetDocument.uid === targetDocumentId &&
            onUpdateTargetDocument(data)
          }
        />
        {canDeleteKPIs && (
          <KPIDeleteButton
            kpi={kpi}
            onDelete={() => {
              const newKpis = kpis.filter((_, i) => i !== index);
              onUpdateTargetDocument({ obj_target_document_kpis: newKpis });
              setKpis(newKpis);
            }}
            targetDocument={targetDocument}
          />
        )}
      </Flex>
    </div>
  );
};
