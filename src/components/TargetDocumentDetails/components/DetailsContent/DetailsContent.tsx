import { Accordion, Grid } from '@ghq-abi/design-system';
import { AccordionWrapper } from './styles';
import { AppraisalAccordionItem } from './AppraisalAccordionItem/AppraisalAccordionItem';
import { TargetDocumentDetailsObjectType } from '../../types';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';
import { AccordionItemMonthlyView } from './AccordionItemMonthlyView/AccordionItemMonthlyView';
import { standardCascadingStatuses } from '../../../../utils';
import { CascadingAccordionItem } from './CascadingAccordionItem/CascadingAccordionItem';
import { AnnualView } from './AnnualView';

export const DetailsContent = ({
  targetDocument,
  openAll,
  full_scope,
}: {
  targetDocument: TargetDocumentDetailsObjectType;
  openAll?: boolean;
  full_scope?: boolean;
}) => {
  const { view, kpis } = useTargetDocumentDetails();
  const isTrackAndMonitoring = ['REVIEW_LEAVER', 'REVIEW_MONITORING'].includes(
    targetDocument.str_status,
  );
  const isCascading = standardCascadingStatuses.includes(
    targetDocument.str_status,
  );
  const defaultValue = openAll
    ? kpis.map(
        (kpi, index) =>
          `item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`,
      )
    : [];

  if (isTrackAndMonitoring && view === 'annual')
    return (
      <Grid gap="sm">
        {kpis.map(targetDocumentKpi => (
          <AnnualView
            {...targetDocumentKpi}
            key={targetDocumentKpi.uid_target_document_kpi}
          />
        ))}
      </Grid>
    );

  return (
    <AccordionWrapper>
      <Accordion
        defaultValue={defaultValue}
        type="multiple"
        style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
      >
        {kpis.map((kpi, index) => {
          if (isTrackAndMonitoring && view === 'monthly') {
            return (
              <AccordionItemMonthlyView
                kpi={kpi}
                key={`item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`}
              />
            );
          }

          if (isCascading)
            return (
              <CascadingAccordionItem
                index={index}
                key={`item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`}
                kpi={kpi}
                full_scope={full_scope}
              />
            );

          return (
            <AppraisalAccordionItem
              index={index}
              key={`item-${kpi.uid}-${kpi.uid_target_document_kpi || index}`}
              kpi={kpi}
            />
          );
        })}
      </Accordion>
    </AccordionWrapper>
  );
};
