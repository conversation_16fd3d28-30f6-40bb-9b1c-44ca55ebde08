import { useTranslation } from 'react-i18next';
import { Input } from 'antd';
import { Tooltip, Text } from '@ghq-abi/design-system';
import { useTargetDocumentDetails } from '../../../hooks/useTargetDocumentDetails';
import SelectV2 from '../../../../selectV2/SelectV2';
import { TextLabel } from '../../../../TextLabel/TextLabel';
import { TextDescription } from '../../../../TextDescription/TextDescription';

import { KpiTypeUuids } from '../../../../../utils';

const ZXV_NR_ZONE_UUID = '56DADB70-16C3-EB11-94B3-501AC53B190E';

export const AppraisalAccordionItemForm = ({
  kpi,
  index,
}: {
  kpi: {
    str_name?: string;
    str_kpi_id?: string;
    int_weight?: number;
    str_scope?: string;
    str_final_value?: string | null;
    flt_final_appraisal?: number | null;
    int_kpi_achievement?: number | null;
    uid_type: string;
    str_kpi_type: string;
  };
  index: number;
}) => {
  const { t } = useTranslation();
  const { editMode, setKpis, kpis } = useTargetDocumentDetails();

  const getKpiFinalAppraisalOptions = ({ str_kpi_type, uid_type }): any[] => {
    const type = str_kpi_type || uid_type;
    if (['Milestone KPI', KpiTypeUuids.MILESTONE].includes(type)) {
      return [
        { label: 0, value: 0 },
        { label: 80, value: 80 },
        { label: 100, value: 100 },
      ];
    }

    if (
      [
        'Dashboard KPI',
        KpiTypeUuids.DASHBOARD,
        'Numerical KPI',
        KpiTypeUuids.NUMERICAL,
      ].includes(type)
    ) {
      return [
        { label: 0, value: 0 },
        { label: 80, value: 80 },
        { label: 90, value: 90 },
        { label: 100, value: 100 },
      ];
    }

    if (['Yes / No', KpiTypeUuids.YES_NO].includes(type)) {
      return [
        { label: 0, value: 0 },
        { label: 100, value: 100 },
      ];
    }

    if (['ZXV NR', ZXV_NR_ZONE_UUID].includes(type)) {
      const arrValues: number[] = [];
      for (let i = 101; i <= 140; i += 1) {
        arrValues.push(i);
      }
      return [
        { label: 0, value: 0 },
        { label: 60, value: 60 },
        { label: 80, value: 80 },
        { label: 90, value: 90 },
        { label: 100, value: 100 },
        ...arrValues.map(value => ({ value, label: value })),
      ];
    }

    return [
      { label: 0, value: 0 },
      { label: 80, value: 80 },
      { label: 90, value: 90 },
      { label: 100, value: 100 },
    ];
  };

  const onUpdate = (data: {
    flt_final_appraisal?: number;
    str_final_value?: string;
  }) => {
    setKpis(
      kpis?.map((kpiItem, i) => {
        if (i === index) return { ...kpiItem, ...data };
        return kpiItem;
      }),
    );
  };

  return (
    <div
      style={{
        width: '100%',
        paddingTop: '15px',
        paddingBottom: '15px',
        paddingLeft: '4px',
        paddingRight: '64px',
        display: 'grid',
        height: '92px',
        gap: '10px',
        gridTemplateColumns: '3fr 2fr 2fr 2fr 2fr 2fr',
      }}
    >
      <div style={{ marginRight: '20px', marginLeft: '10px' }}>
        <TextLabel className="truncate-2">{kpi.str_name || ''}</TextLabel>
        <TextDescription>{kpi.str_kpi_id}</TextDescription>
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_weight')}</TextLabel>
        <TextDescription>{`${kpi.int_weight ?? 0} %`}</TextDescription>
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_scope')}</TextLabel>
        <Tooltip
          content={
            (
              <Text
                css={{
                  color: '$white',
                  fontSize: '0.75rem',
                  fontWeight: '$bold',
                }}
                dangerouslySetInnerHTML={{
                  __html: kpi.str_scope || '',
                }}
              />
            ) as never
          }
          side="top"
        >
          <TextDescription className="truncate-2">
            {kpi.str_scope || ''}
          </TextDescription>
        </Tooltip>
      </div>
      <div
        style={{
          display: 'grid',
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
        }}
      >
        <TextLabel>{t('common_final_value')}</TextLabel>

        {editMode ? (
          <Input
            size="large"
            value={kpi.str_final_value ?? ''}
            onChange={e => onUpdate({ str_final_value: e.target.value })}
          />
        ) : (
          <TextDescription className="truncate-2">
            {kpi.str_final_value ?? ''}
          </TextDescription>
        )}
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_kpi_final_appraisal')}</TextLabel>
        {editMode ? (
          <SelectV2
            showSearch
            size="large"
            placeholder={t('common_kpi_final_appraisal')}
            getPopupContainer={trigger => trigger.parentNode}
            options={getKpiFinalAppraisalOptions(kpi)}
            onClick={e => e.stopPropagation()}
            value={kpi.flt_final_appraisal}
            onChange={value =>
              onUpdate({ flt_final_appraisal: +(value as string) })
            }
          />
        ) : (
          <TextDescription className="truncate-2">
            {kpi.flt_final_appraisal !== null &&
            kpi.flt_final_appraisal !== undefined
              ? `${kpi.flt_final_appraisal} %`
              : ''}
          </TextDescription>
        )}
      </div>
      <div
        style={{
          borderLeft: '1px solid #EEEFF2',
          paddingLeft: '8px',
          height: 'fit-content',
          display: 'grid',
        }}
      >
        <TextLabel>{t('common_kpi_final_achievement')}</TextLabel>
        <TextDescription className="truncate-2">
          {kpi.int_kpi_achievement ?? ''}
        </TextDescription>
      </div>

      {/** TODO */}
      {/* <Flex
        style={{ position: 'absolute', right: '8px', top: '40px' }}
        align="end"
      >
        <CardItemDetailsKPIListItemAssignButton kpi={kpi} />
        <CardItemDetailsKPIListItemDeleteButton kpi={kpi} index={index} />
      </Flex> */}
    </div>
  );
};
