import { Accordion, Tooltip, Text } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { useTargetDocumentDetails } from '../../../hooks/useTargetDocumentDetails';
import { StyledTrigger } from '../AppraisalAccordionItem/styles';
import { TextLabel } from '../../../../TextLabel/TextLabel';
import { TextDescription } from '../../../../TextDescription/TextDescription';

import trackMonitoringService from '../../../../../services/trackMonitoring/trackMonitoringService';
import { TargetDocumentDetailsKPIType } from '../../../types';
import { TrafficLightPercentageMap } from '../AnnualView/common';
import { TrafficLightDropdown } from '../AnnualView/TrafficLightsDropdown';

export const AccordionItemMonthlyView = ({
  kpi,
}: {
  kpi: TargetDocumentDetailsKPIType;
}) => {
  const { month, kpis } = useTargetDocumentDetails();
  const { t } = useTranslation();
  const [isHandleChangeStatusDisabled, setIsHandleChangeStatusDisabled] =
    useState(false);

  const [monitoringState, setMonitoringState] = useState<any>();
  const localCache = useRef<Record<string, any>>({});

  useEffect(() => {
    const monitoring = kpi.obj_monitoring?.find((obj: any) => {
      return new Date(obj.dat_month_year).getUTCMonth() === month;
    });

    const cachedMonitoring = localCache.current[month];
    setMonitoringState(cachedMonitoring || monitoring);
  }, [month, kpi.obj_monitoring, kpis]);

  const handleChangeStatus = async (value: number, field: string) => {
    message.loading({
      content: t('message_action_progress'),
      key: 'update',
    });
    setIsHandleChangeStatusDisabled(true);

    const { uid_target_document, uid_target_document_kpi } = kpi;
    const dayOneOfSelectedMonth = moment({
      month,
    })
      .date(1)
      .format('YYYY-MM-DD');

    const body = {
      uid_target_document,
      uid_target_document_kpi,
      dat_month_year: dayOneOfSelectedMonth,
      uid: uid_target_document_kpi,
      [field]: value,
      bol_can_edit: 1,
    };

    try {
      const res = await trackMonitoringService.completeMontlyMonitoring(body);

      if (res.success) {
        message.success({
          content: t('message_save'),
          key: 'update',
          duration: 5,
        });

        const updatedMonitoringState = {
          ...monitoringState,
          [field]: value,
        };

        localCache.current[month] = updatedMonitoringState;

        setMonitoringState(updatedMonitoringState);
      } else {
        message.error({
          content: res.errorMessage,
          key: 'update',
          duration: 15,
        });
      }
    } catch (error) {
      message.error({
        content: t('message_error'),
        key: 'update',
        duration: 15,
      });
    } finally {
      setIsHandleChangeStatusDisabled(false);
    }
  };

  return (
    <Accordion.Item
      style={{ margin: 0 }}
      key={kpi.uid_target_document_kpi}
      value={`item-${kpi.uid_target_document_kpi}`}
    >
      <div style={{ display: 'flex', background: 'white' }}>
        <div
          style={{
            width: '100%',
            paddingTop: '15px',
            paddingBottom: '15px',
            paddingLeft: '4px',
            paddingRight: '64px',
            display: 'grid',
            height: '92px',
            gap: '10px',
            gridTemplateColumns: '3fr 2fr 2fr 2fr 2fr 2fr',
          }}
        >
          <div style={{ marginRight: '20px', marginLeft: '10px' }}>
            <TextLabel className="truncate-2">{kpi.str_name}</TextLabel>
            <TextDescription>{kpi.str_kpi_id}</TextDescription>
          </div>
          <div
            style={{
              borderLeft: '1px solid #EEEFF2',
              paddingLeft: '8px',
              height: 'fit-content',
              display: 'grid',
            }}
          >
            <TextLabel>{t('common_weight')}</TextLabel>
            <TextDescription>{`${kpi.int_weight || 0} %`}</TextDescription>
          </div>
          <div
            style={{
              borderLeft: '1px solid #EEEFF2',
              paddingLeft: '8px',
              height: 'fit-content',
              display: 'grid',
            }}
          >
            <TextLabel>{t('common_value')}</TextLabel>
            <TextDescription>{kpi.str_value}</TextDescription>
          </div>
          <div
            style={{
              borderLeft: '1px solid #EEEFF2',
              paddingLeft: '8px',
              height: 'fit-content',
              display: 'grid',
            }}
          >
            <TextLabel>{t('common_scope')}</TextLabel>
            <Tooltip
              content={
                (
                  <Text
                    css={{
                      color: '$white',
                      fontSize: '0.75rem',
                      fontWeight: '$bold',
                    }}
                    dangerouslySetInnerHTML={{
                      __html: kpi.str_scope || '',
                    }}
                  />
                ) as never
              }
              side="top"
            >
              <TextDescription className="truncate-2">
                {kpi.str_scope || ''}
              </TextDescription>
            </Tooltip>{' '}
          </div>
          <div
            style={{
              borderLeft: '1px solid #EEEFF2',
              paddingLeft: '8px',
              width: 'fit-content',
              height: 'fit-content',
              display: 'grid',
            }}
          >
            <TextLabel>{t('common_ytd')}</TextLabel>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                textAlign: 'center',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '0.25rem',
              }}
            >
              <TrafficLightDropdown
                value={monitoringState?.int_value ?? null}
                onStatusChange={handleChangeStatus}
                isDisabled={isHandleChangeStatusDisabled}
              />
              {monitoringState?.int_value !== null &&
              monitoringState?.int_value !== undefined
                ? TrafficLightPercentageMap[monitoringState.int_value]
                : '-'}
            </div>
          </div>
          <div
            style={{
              borderLeft: '1px solid #EEEFF2',
              paddingLeft: '8px',
              height: 'fit-content',
              width: 'fit-content',
              display: 'grid',
            }}
          >
            <TextLabel>{t('common_le')}</TextLabel>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                textAlign: 'center',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '0.25rem',
              }}
            >
              <TrafficLightDropdown
                value={monitoringState?.int_value_le ?? null}
                onStatusChange={handleChangeStatus}
                isDisabled={isHandleChangeStatusDisabled}
                valueType="int_value_le"
              />
              {monitoringState?.int_value_le !== null &&
              monitoringState?.int_value_le !== undefined
                ? TrafficLightPercentageMap[monitoringState.int_value_le]
                : '-'}
            </div>
          </div>
        </div>
        <StyledTrigger />
      </div>
      <Accordion.Content>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 3fr',
            gap: '8px',
          }}
        >
          <div>
            <div>
              <TextLabel>{t('common_value')}</TextLabel>
              <TextDescription>{kpi.str_value}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_business_function')}</TextLabel>
              <TextDescription>
                {kpi.str_business_function_name}
              </TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_type')}</TextLabel>
              <TextDescription>{kpi.str_kpi_type}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('common_source')}</TextLabel>
              <TextDescription className="truncate-4">
                {kpi.str_source_name}
              </TextDescription>
            </div>
          </div>
          <div>
            <div>
              <TextLabel>{t('common_pa_value')}</TextLabel>
              <TextDescription>{kpi.str_pa_value}</TextDescription>
            </div>
            <div>
              <TextLabel>{t('kpi_calculation_method')}</TextLabel>
              <TextDescription>{kpi.str_calculation_method}</TextDescription>
            </div>
          </div>
        </div>
      </Accordion.Content>
    </Accordion.Item>
  );
};
