import { Flex, Text } from '@ghq-abi/design-system';
import { XNewLgIcon } from '@ghq-abi/design-system-icons';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

export const AlertPreAssignation = ({ isOwner }: { isOwner?: boolean }) => {
  const [, setOpen] = useState(true);
  const { t } = useTranslation();

  if (isOwner) return null;

  return (
    <Flex
      justify="between"
      css={{
        backgroundColor: '#D3E5FE',
        borderRadius: '4px',
        height: '53px',
        padding: '8px 16px',
      }}
    >
      <Flex direction="column">
        <Text css={{ fontWeight: '$bold', fontSize: '$2' }}>
          {t('message_shared_target_pre_assignation_title_dont_worry')}
        </Text>
        <Text css={{ fontWeight: '$normal', fontSize: '$1' }}>
          {t('message_shared_target_pre_assignation_dont_worry')}
        </Text>
      </Flex>
      <XNewLgIcon
        style={{ cursor: 'pointer', alignSelf: 'center' }}
        width={18}
        height={18}
        onClick={() => setOpen(false)}
      />
    </Flex>
  );
};
