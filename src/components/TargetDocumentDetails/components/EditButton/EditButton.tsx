import { PencilIcon } from '@ghq-abi/design-system-icons';
import { IconButtonV2, Text, Tooltip } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useTargetDocumentBlocker } from '../../../TargetDocumentBlocker/useTargetDocumentBlocker';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';

export const EditButton = () => {
  const { t } = useTranslation();
  const { targetDocument, editMode, setEditMode } = useTargetDocumentDetails();
  const { openWarning } = useTargetDocumentBlocker(targetDocument);

  return (
    <Tooltip
      content={
        (
          <Text
            css={{
              color: '$white',
              fontSize: '0.75rem',
              fontWeight: '$bold',
            }}
            dangerouslySetInnerHTML={{
              __html: t('common_edit'),
            }}
          />
        ) as any
      }
      side="top"
    >
      <IconButtonV2
        size="md"
        variant={editMode ? 'primary' : 'ghost'}
        rounded
        onClick={() => {
          if (!openWarning()) setEditMode(!editMode);
        }}
        icon={<PencilIcon />}
      />
    </Tooltip>
  );
};
