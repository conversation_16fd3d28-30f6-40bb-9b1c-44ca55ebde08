import { IconButtonV2, Text, Tooltip } from '@ghq-abi/design-system';
import { PlusIcon } from '@ghq-abi/design-system-icons';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KPIAssignModal } from '../../../KPIAssignModal/KPIAssignModal';
import { IListCatalogKPI } from '../../../../services/kpi/types';

export const KPIAddButton = ({
  targetDocument,
  onAdded,
  full_scope,
}: {
  targetDocument: { dat_finish: string };
  onAdded: (selecteds: IListCatalogKPI[]) => void;
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <>
      <Tooltip
        content={
          (
            <Text
              css={{
                color: '$white',
                fontSize: '0.75rem',
                fontWeight: '$bold',
              }}
              dangerouslySetInnerHTML={{
                __html: t('common_add'),
              }}
            />
          ) as any
        }
        side="top"
      >
        <IconButtonV2
          size="md"
          variant="ghost"
          onClick={() => setModalVisible(true)}
          icon={<PlusIcon style={{ display: 'flex' }} />}
          rounded
          showActiveOutline={false}
        />
      </Tooltip>
      <KPIAssignModal
        kpisYear={targetDocument.dat_finish?.slice(0, 4)}
        open={modalVisible}
        onClose={() => setModalVisible(false)}
        onSelectMultiple={onAdded}
        selectMultiple
        full_scope={full_scope}
      />
    </>
  );
};
