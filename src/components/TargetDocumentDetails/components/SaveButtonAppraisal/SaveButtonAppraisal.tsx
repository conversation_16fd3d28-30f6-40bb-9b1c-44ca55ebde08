import { ButtonV2 } from '@ghq-abi/design-system';
import { useState } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { TargetDocumentDetailsKPIType } from '../../types';
import appraisalService from '../../../../services/appraisal/appraisalService';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';

export const SaveButtonAppraisal = ({
  kpis,
  onAfterSave,
}: {
  kpis: TargetDocumentDetailsKPIType[];
  onAfterSave: () => void;
}) => {
  const { targetDocument } = useTargetDocumentDetails();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleAppraisalCompletionUpdate = async () => {
    message.loading(
      { content: t('message_action_progress'), key: 'update' },
      0,
    );
    setLoading(true);

    try {
      if (targetDocument.str_status === 'APPRAISAL_NOT_STARTED') {
        const body = kpis.map(kpi => ({
          uid_target_document_kpi: kpi.uid_target_document_kpi,
          flt_final_appraisal: kpi.flt_final_appraisal,
          str_final_value: kpi.str_final_value,
          uid_target_document: kpi.uid_target_document,
          uid_kpi: kpi.uid,
          str_score: kpi.str_score,
        }));
        await appraisalService.editTargetDocumentEmployee(body);
      } else {
        const body = {
          uid_target_document: targetDocument.uid,
          obj_target_document: kpis.map(kpi => ({
            uid_target_document_kpi: kpi.uid_target_document_kpi,
            flt_final_appraisal: kpi.flt_final_appraisal,
            str_final_value: kpi.str_final_value,
          })),
        };
        await appraisalService.appraisalCompletionUpdate(body);
      }
      onAfterSave();
      message.success({
        content: t('message_save'),
        key: 'update',
        duration: 5,
      });
    } catch (err) {
      message.error({
        content:
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (err as any).errorMoessage ??
          (err as Error).message ??
          t('request_error'),
        key: 'update',
        duration: 15,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <ButtonV2
      variant="primary"
      size="md"
      loading={loading}
      onClick={handleAppraisalCompletionUpdate}
    >
      {t('common_save_edit')}
    </ButtonV2>
  );
};
