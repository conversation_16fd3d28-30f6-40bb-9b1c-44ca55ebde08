import { Flex, Tooltip, Text } from '@ghq-abi/design-system';
import { ChatAlertIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';

export const SendToAppraiserApprovalErrorBadge = ({
  kpiErrors,
}: {
  kpiErrors: string | null;
}) => {
  const { t } = useTranslation();

  return (
    <Tooltip
      content={
        (
          <Text
            css={{
              color: '$white',
              fontSize: '0.75rem',
              fontWeight: '$bold',
            }}
            dangerouslySetInnerHTML={{
              __html: kpiErrors ?? '',
            }}
          />
        ) as any
      }
      side="top"
    >
      <Flex
        align="center"
        gap="sm"
        css={{
          background: '#FBFAFC',
          borderRadius: '200px',
          padding: '1rem',
          height: '40px',
        }}
      >
        <ChatAlertIcon width={20} height={20} />
        <Text
          css={{
            fontSize: '0.75rem',
            fontWeight: '$bold',
            color: '$red',
          }}
        >
          {t('kpis_errors')}
        </Text>
      </Flex>
    </Tooltip>
  );
};
