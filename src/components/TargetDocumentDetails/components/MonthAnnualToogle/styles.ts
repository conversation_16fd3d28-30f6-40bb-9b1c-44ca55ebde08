import { Select, styled } from '@ghq-abi/design-system';
import * as ToggleGroup from '@radix-ui/react-toggle-group';

export const StyledToggleGroup = styled(ToggleGroup.Root, {
  display: 'inline-flex',
  backgroundColor: '$white',
  border: '1px solid #325A6D',
  borderRadius: '0.25rem',
  boxShadow: '0px 1px 1px 0px #00000014',
  overflow: 'hidden',
  height: '2.5rem',
  width: 'fit-content',
});

export const StyledToggleGroupItem = styled(ToggleGroup.Item, {
  height: '2.5rem',
  border: 'none',
  color: '#325A6D',
  backgroundColor: '$white',
  margin: 0,
  fontWeight: 600,
  fontSize: '1rem',
  '&:hover': {
    backgroundColor: '#325A6D',
    color: '$white',
  },
  '&[data-state="on"]': {
    backgroundColor: '#325A6D',
    color: '$white',
  },
});

export const StyledMonthSelectTrigger = styled(Select.Trigger, {
  border: 'none',
  color: '#325A6D',
  backgroundColor: '$white',
  margin: 0,
  fontWeight: 400,
  fontSize: '1rem',
  padding: '0 1rem',
  height: '2.5rem',
  display: 'flex',
  direction: 'row',
  gap: '0.5rem',
  '&:focus': {
    boxShadow: 'none',
  },
});
