import { useTranslation } from 'react-i18next';
import { Flex, Select } from '@ghq-abi/design-system';
import { CalendarIcon } from '@ghq-abi/design-system-icons';
import { message } from 'antd';
import { useTargetDocumentDetails } from '../../hooks/useTargetDocumentDetails';
import {
  StyledMonthSelectTrigger,
  StyledToggleGroup,
  StyledToggleGroupItem,
} from './styles';
import { colors } from '../../../../styles/theme';
import { monthsNames } from '../../../../utils/monthsNames';
import targetDocumentService from '../../../../services/targetDocument/targetDocumentService';

export const MonthAnnualToogle = () => {
  const { t } = useTranslation();
  const { view, setView, month, setMonth, targetDocument, setKpis } =
    useTargetDocumentDetails();

  const refetchTargetDocument = async (targetDocumentId: string) => {
    try {
      const td = await targetDocumentService.getTargetDocumentDetails(
        targetDocumentId,
      );
      setKpis(td.data.obj_target_document_kpis);
    } catch (e) {
      message.error({
        content: (e as Error).message || t('request_error'),
        duration: 15,
        type: 'error',
        onClick: () => message.destroy(),
      });
    }
  };

  const handleChangeToglee = (value: 'monthly' | 'annual') => {
    if (targetDocument) refetchTargetDocument(targetDocument.uid);
    setView(value);
  };

  const getMonthsOptions = () =>
    monthsNames.map((monthName, index) => (
      <Select.Item
        key={monthName.complete}
        value={`${index}`}
        style={{ boxShadow: 'none' }}
      >
        <Select.ItemText>{t(monthName.short)}</Select.ItemText>
      </Select.Item>
    ));

  return (
    <Flex gap="sm">
      <StyledToggleGroup
        type="single"
        defaultValue="center"
        aria-label="Switch between monthly and annual view"
      >
        <StyledToggleGroupItem
          key="monthly"
          value="monthly"
          aria-label="Monthly view"
          data-state={view === 'monthly' ? 'on' : 'off'}
          onClick={() => handleChangeToglee('monthly')}
        >
          {t('common_monthly_view')}
        </StyledToggleGroupItem>
        <StyledToggleGroupItem
          key="annual"
          value="annual"
          aria-label="Annual view"
          data-state={view === 'annual' ? 'on' : 'off'}
          onClick={() => handleChangeToglee('annual')}
        >
          {t('common_annual_view')}
        </StyledToggleGroupItem>
      </StyledToggleGroup>
      {view === 'monthly' && (
        <Select onValueChange={value => setMonth(+value)} value={`${month}`}>
          <StyledMonthSelectTrigger aria-label="Month">
            <Select.Value placeholder={t('common_month')} />
            <CalendarIcon color={colors.mediumBlue} />
          </StyledMonthSelectTrigger>
          <Select.Content>
            <Select.Viewport>{getMonthsOptions()}</Select.Viewport>
          </Select.Content>
        </Select>
      )}
    </Flex>
  );
};
