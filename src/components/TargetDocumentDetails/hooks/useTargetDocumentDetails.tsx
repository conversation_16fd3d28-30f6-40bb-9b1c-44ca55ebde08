import {
  Dispatch,
  SetStateAction,
  createContext,
  useContext,
  useState,
} from 'react';
import {
  TargetDocumentDetailsKPIType,
  TargetDocumentDetailsObjectType,
} from '../types';
import { useTargetDocumentSelection } from '../../../hooks/useTargetDocumentSelection';
import { TargetDocumentListItem } from '../../../services/targetDocument/types';

type ViewMode = 'monthly' | 'annual';

type TargetDocumentDetailsContextType = {
  targetDocument: TargetDocumentDetailsObjectType;
  onUpdate: (newData: Partial<TargetDocumentDetailsObjectType>) => void;
  kpis: TargetDocumentDetailsKPIType[];
  setKpis: (newKpis: TargetDocumentDetailsKPIType[]) => void;
  editMode: boolean;
  setEditMode: Dispatch<SetStateAction<boolean>>;
  view: ViewMode;
  setView: (view: ViewMode) => void;
  month: number;
  setMonth: (month: number) => void;
  canAddKPIs?: boolean;
  canDeleteKPIs?: boolean;
};

const TargetDocumentDetailsContext = createContext(
  {} as TargetDocumentDetailsContextType,
);

export const TargetDocumentDetailsProvider = ({
  children,
  targetDocument,
  onUpdate,
  canAddKPIs,
  canDeleteKPIs,
}: React.PropsWithChildren<{
  targetDocument: TargetDocumentDetailsObjectType;
  onUpdate: (newData: Partial<TargetDocumentListItem>) => void;
  canAddKPIs?: boolean;
  canDeleteKPIs?: boolean;
}>) => {
  const [kpis, setKpis] = useState<TargetDocumentDetailsKPIType[]>(
    targetDocument.obj_target_document_kpis || [],
  );
  const [view, setView] = useState<ViewMode>('monthly');
  const [month, setMonth] = useState<number>(new Date().getMonth());
  const [editMode, setEditMode] = useState<boolean>(false);
  const { selected, setSelected } = useTargetDocumentSelection();

  return (
    <TargetDocumentDetailsContext.Provider
      // eslint-disable-next-line react/jsx-no-constructed-context-values
      value={{
        kpis: kpis?.map(kpi => {
          const x = Number(kpi.int_weight) || 0;
          const y = Number(kpi.flt_final_appraisal) || 0;

          return { ...kpi, int_kpi_achievement: +((x * y) / 100).toFixed(2) };
        }),
        setKpis,
        editMode,
        setEditMode,
        view,
        setView,
        month,
        setMonth,
        targetDocument,
        onUpdate: (data: Partial<TargetDocumentDetailsObjectType>) => {
          setSelected({ ...selected!, ...data });
          onUpdate(data);
        },
        canAddKPIs,
        canDeleteKPIs,
      }}
    >
      {children}
    </TargetDocumentDetailsContext.Provider>
  );
};

export const useTargetDocumentDetails = () => {
  const context = useContext(TargetDocumentDetailsContext);

  if (context === undefined) {
    throw new Error(
      'useTargetDocumentDetails must be used within a TargetDocumentDetailsProvider',
    );
  }
  return context;
};
