import { Flex } from '@ghq-abi/design-system';
import { Empty } from 'antd';
import React from 'react';
import { isNull } from 'lodash';
import {
  TargetDocumentDetailsProvider,
  useTargetDocumentDetails,
} from './hooks/useTargetDocumentDetails';
import { DetailsContent } from './components/DetailsContent/DetailsContent';
import { TargetDocumentDetailsObjectType } from './types';
import { EmployeeHeader } from './components/EmployeeHeader/EmployeeHeader';
import { AlertPreAssignation } from './components/AlertPreAssignation/AlertPreAssignation';
import { EmptyKPIsSection } from './components/EmptyKPIsSection/EmptyKPIsSection';
import { TargetDocumentListItem } from '../../services/targetDocument/types';

export const TargetDocumentDetails = ({
  targetDocument,
  onUpdate,
  actionsBar,
  canAddKPIs,
  canDeleteKPIs,
  full_scope,
}: {
  targetDocument: TargetDocumentDetailsObjectType;
  onUpdate: (newData: Partial<TargetDocumentListItem>) => void;
  actionsBar: React.ReactElement;
  canAddKPIs?: boolean;
  canDeleteKPIs?: boolean;
  full_scope?: boolean;
}) => (
  <Flex
    direction="column"
    style={{ minWidth: '100%', paddingLeft: '2rem', gap: '1rem' }}
  >
    {!isNull(targetDocument.id_spoc) && (
      <AlertPreAssignation
        isOwner={targetDocument.bol_pre_assignation_book_owner}
      />
    )}
    <TargetDocumentDetailsProvider
      key={targetDocument.uid}
      onUpdate={onUpdate}
      targetDocument={targetDocument}
      canAddKPIs={canAddKPIs}
      canDeleteKPIs={canDeleteKPIs}
    >
      <EmployeeHeader />
      {actionsBar}
      <TargetDocumentDetailsContent full_scope={full_scope} />
    </TargetDocumentDetailsProvider>
  </Flex>
);

function TargetDocumentDetailsContent({
  full_scope,
}: {
  full_scope?: boolean;
}) {
  const { kpis, canAddKPIs, targetDocument } = useTargetDocumentDetails();
  const isEmptyKPIS = kpis.length === 0;
  return (
    <Flex direction="column" style={{ overflow: 'auto' }}>
      {isEmptyKPIS ? (
        canAddKPIs ? (
          <EmptyKPIsSection full_scope={full_scope} />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )
      ) : (
        <DetailsContent
          targetDocument={targetDocument}
          full_scope={full_scope}
        />
      )}
    </Flex>
  );
}
