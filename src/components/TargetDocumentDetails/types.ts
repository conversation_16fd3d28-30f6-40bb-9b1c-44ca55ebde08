import {
  TargetDocumentDetailsResponseKPIType,
  TargetDocumentDetailsResponseType,
} from '../../services/targetDocument/types';

export type TargetDocumentDetailsObjectType = Omit<
  TargetDocumentDetailsResponseType,
  'obj_target_document_kpis'
> & {
  obj_target_document_kpis: TargetDocumentDetailsKPIType[];
};

export type TargetDocumentDetailsKPIType = Pick<
  TargetDocumentDetailsResponseKPIType,
  | 'uid'
  | 'uid_type'
  | 'str_business_function_name'
  | 'str_kpi_type'
  | 'str_calculation_method'
  | 'bol_can_edit'
> &
  Partial<TargetDocumentDetailsResponseKPIType> & {
    str_kpi_name?: string; // It only exists in the kpi catalog object;
    str_source?: string; // It only exists in the kpi catalog object;
  };
