import { ButtonV2, config } from '@ghq-abi/design-system';
import { NineDotsIcon } from '@ghq-abi/design-system-icons';
import { useState } from 'react';
import Input from 'antd/lib/input/Input';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ModalV2 } from '..';
import { TextLabel } from '../TextLabel/TextLabel';
import { TextDescription } from '../TextDescription/TextDescription';

function extractNumbersAndCommas(input: string): string {
  const result = input.match(/[0-9,]+/g);
  return result ? result.join('') : '';
}

const StyleInput = styled(Input)`
  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  -moz-appearance: textfield;
`;

export const InsertIdsButton = ({
  searchValue,
  onUpdateSearch,
}: {
  searchValue: string;
  onUpdateSearch: (newValue: string) => void;
}) => {
  const { t } = useTranslation();
  const [modalOpen, setModalOpen] = useState(false);
  const normalizedInput = extractNumbersAndCommas(searchValue);

  const [ids, setIds] = useState(
    normalizedInput.length && normalizedInput.length === searchValue.length
      ? normalizedInput.split(',').concat([''])
      : [''],
  );

  const onUpdateId = (value: string, index: number) => {
    const newIds = ids.map((id, i) => (i === index ? value : id));

    if (value.includes(' ')) {
      newIds.splice(index, 1, ...value.split(' '));
    }

    const lastId = newIds.at(-1);
    const lastLastId = newIds.at(-2);

    if (lastId) {
      newIds.push('');
    }

    if (lastId === '' && lastLastId === '') {
      newIds.pop();
    }
    setIds(newIds);
  };

  const newSearchValue = ids.slice(0, -1).join(',');

  return (
    <>
      <ButtonV2
        onClick={() => setModalOpen(true)}
        variant="secondary"
        size="md"
        leftIcon={<NineDotsIcon />}
        style={{
          gap: '0px',
          paddingLeft: config.theme.space['2-5'],
          paddingRight: config.theme.space['2-5'],
        }}
      />
      <ModalV2
        open={modalOpen}
        title={t('common_insert_employee_ids')}
        hasFooter={false}
        onCancel={() => setModalOpen(false)}
        zIndex={99999}
      >
        <>
          <div
            style={{
              display: 'flex',
              gap: config.theme.space[2],
              marginBottom: config.theme.space[4],
            }}
          >
            <TextLabel>
              {ids.length - 1 > 1
                ? t('common_employees')
                : t('common_employee')}
            </TextLabel>
            <TextDescription>{ids.length - 1}</TextDescription>
          </div>
          <div
            style={{
              background: 'white',
              padding: config.theme.space[4],
              display: 'grid',
              gap: config.theme.space[2],
            }}
          >
            {ids.map((idStr, index) => (
              <StyleInput
                // eslint-disable-next-line react/no-array-index-key
                key={`id-${index}`}
                placeholder={t('common_insert_id_here')}
                size="large"
                value={idStr}
                onChange={e => onUpdateId(e.target.value, index)}
              />
            ))}
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: config.theme.space[4],
            }}
          >
            <ButtonV2
              size="md"
              variant="secondary"
              onClick={() => setModalOpen(false)}
            >
              {t('common_cancel')}
            </ButtonV2>
            <div style={{ display: 'flex', gap: config.theme.space[3] }}>
              <ButtonV2
                size="md"
                variant="secondary"
                onClick={() => setIds([''])}
              >
                {t('common_reset')}
              </ButtonV2>
              <ButtonV2
                size="md"
                disabled={newSearchValue === searchValue}
                onClick={() => {
                  onUpdateSearch(newSearchValue);
                  setModalOpen(false);
                }}
              >
                {t('common_insert')}
              </ButtonV2>
            </div>
          </div>
        </>
      </ModalV2>
    </>
  );
};
