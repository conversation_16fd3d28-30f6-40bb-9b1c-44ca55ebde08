import { Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { MouseEventHandler } from 'react';
import { createKpiValueOptionsByKpiType, KpiTypeUuids } from '../../utils';
import SelectV2 from '../selectV2/SelectV2';
import { KPI_VALUE_NUMERICAL_TYPES } from '../../constants';

export const ValueField = ({
  selectedKPI,
  value,
  onChange,
  ...rest
}: {
  selectedKPI: { uid_type?: string };
  value: string | number | undefined;
  onChange: (value: number | string | undefined) => void;
  onClick?: MouseEventHandler;
}) => {
  const { t } = useTranslation();

  if (
    KPI_VALUE_NUMERICAL_TYPES.includes(selectedKPI.uid_type as KpiTypeUuids)
  ) {
    const kpiValueOptions = createKpiValueOptionsByKpiType(
      selectedKPI.uid_type,
    );

    return (
      <SelectV2
        showSearch
        size="large"
        style={{ width: '100%' }}
        placeholder={t('common_select_option')}
        value={+(value || 0)}
        options={kpiValueOptions}
        onChange={val => onChange(val as number)}
        {...rest}
      />
    );
  }
  return (
    <Input
      style={{ width: '100%' }}
      name="str_value"
      value={value}
      size="large"
      onChange={e => onChange(e.target.value)}
      {...rest}
    />
  );
};
