import { Grid, Text } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { TargetDocumentDetailsKPIType } from '../TargetDocumentDetails/types';
import { getTrafficLight } from '../TargetDocumentDetails/components/DetailsContent/AnnualView/AnnualView';
import { StyledTextBold } from './styles';

type MonitoringType = {
  dat_month_year: string | null;
  int_value: number | null;
  int_value_le: number | null;
};

export const KPISTableMonitoringAnnualRow = ({
  kpi,
}: {
  kpi: TargetDocumentDetailsKPIType;
}) => {
  const dateNow = new Date();
  const currentYear = dateNow.getFullYear();
  const currentMonth = dateNow.getMonth();
  const { t } = useTranslation();

  const getFirstDayOfMonth = (month: number, year?: number) =>
    new Date(year || currentYear, month, 1).toISOString().substring(0, 10);

  const fillEmptyMonitoring = (monitorings: Array<MonitoringType>) => {
    const fullyEmpty = Array.from<MonitoringType, MonitoringType>(
      { length: 12 },
      (_, k) => ({
        dat_month_year: getFirstDayOfMonth(k),
        int_value: null,
        int_value_le: null,
      }),
    );

    if (monitorings) {
      fullyEmpty.forEach((empty, i) => {
        const monitoring = monitorings.find(
          m => m.dat_month_year?.substring(0, 10) === empty.dat_month_year,
        );

        if (monitoring) {
          fullyEmpty[i] = monitoring;
        }
      });
    }

    return fullyEmpty;
  };

  return (
    <Grid css={{ borderBottom: '1px solid #EEEFF2' }}>
      <Grid
        align="center"
        gap="sm"
        css={{
          gridTemplateColumns: '4fr repeat(13, 1fr)',
          padding: '10px 16px',
        }}
        key={`kpi-${kpi.uid}-index`}
      >
        <Text>{kpi.str_name || kpi.str_kpi_name || ''}</Text>
        <Grid>
          <StyledTextBold>{t('common_year_to_date_abv')}</StyledTextBold>
          <StyledTextBold>{t('common_le')}</StyledTextBold>
        </Grid>
        {kpi.obj_monitoring &&
          fillEmptyMonitoring(kpi.obj_monitoring).map((tm, i) => (
            <Grid align="center" gap="sm" key={tm.dat_month_year}>
              {getTrafficLight(tm.int_value, {}, i === currentMonth)}
              {getTrafficLight(tm.int_value_le, {}, i === currentMonth)}
            </Grid>
          ))}
      </Grid>
    </Grid>
  );
};
