import { ArrowsFullscreenIcon } from '@ghq-abi/design-system-icons';
import { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flex, Grid, Text, Tooltip } from '@ghq-abi/design-system';
import ModalV2 from '../modalV2/ModalV2';
import { StyledIconButton, StyledModalContent, StyledTextBold } from './styles';
import { TargetDocumentDetailsObjectType } from '../TargetDocumentDetails/types';
import { TargetDocumentAccordion } from './TargetDocumentAccordion';
import { KPISTableCascading } from './KPISTableCascading';
import { standardCascadingStatuses } from '../../utils';
import { KPISTableAppraisal } from './KPISTableAppraisal';
import { Avatar } from '../avatar';
import { STATUS_CAPTION_ICON } from '../TargetDocumentList/constants';
import configs from '../../services/configs';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPISTableMonitoringAnnual } from './KPISTableMonitoringAnnual';
import { KPISTableMonitoringMonthly } from './KPISTableMonitoringMonthly';

export const ExpandViewButton = ({
  targetDocument,
  employeeHeaderRightSlot,
  actionsSlot,
  full_scope,
}: {
  targetDocument: TargetDocumentDetailsObjectType;
  employeeHeaderRightSlot?: ReactElement;
  actionsSlot?: ReactElement;
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const { view } = useTargetDocumentDetails();

  const isCascading = standardCascadingStatuses.includes(
    targetDocument.str_status,
  );
  const isTrackAndMonitoring = ['REVIEW_LEAVER', 'REVIEW_MONITORING'].includes(
    targetDocument.str_status,
  );

  const avatarInfo = {
    name: targetDocument.str_employee_name,
    photoUrl:
      configs.photo_api_url && targetDocument.int_employee_global_id
        ? `${configs.photo_api_url}/${targetDocument.int_employee_global_id}`
        : undefined,
  };

  const renderTable = () => {
    if (isTrackAndMonitoring && view === 'annual') {
      return <KPISTableMonitoringAnnual />;
    }
    if (isTrackAndMonitoring && view === 'monthly') {
      return <KPISTableMonitoringMonthly />;
    }
    if (isCascading) {
      return <KPISTableCascading full_scope={full_scope} />;
    }

    return <KPISTableAppraisal />;
  };

  return (
    <>
      <Tooltip
        content={
          (
            <Text
              css={{
                color: '$white',
                fontSize: '0.75rem',
                fontWeight: '$bold',
              }}
              dangerouslySetInnerHTML={{
                __html: t('common_expand'),
              }}
            />
          ) as any
        }
        side="top"
      >
        <StyledIconButton
          icon={<ArrowsFullscreenIcon />}
          size="md"
          onClick={() => setOpen(true)}
          variant="ghost"
          rounded
          showActiveOutline={false}
        />
      </Tooltip>
      <ModalV2
        width="calc(100% - 200px)"
        open={open}
        title={t('common_wide_list_view')}
        hasFooter={false}
        onCancel={() => setOpen(false)}
      >
        <StyledModalContent>
          <Flex justify="between">
            <Flex gap="md" align="center">
              <Avatar
                size="sm"
                name={avatarInfo.name}
                enablePreLoading
                src={avatarInfo.photoUrl}
                imageCss={{
                  backgroundColor: 'transparent',
                  backgroundImage: 'none',
                }}
              />
              <Grid>
                <StyledTextBold>
                  {targetDocument.str_employee_name.toUpperCase()}
                </StyledTextBold>
                <Flex align="center" gap="sm">
                  {STATUS_CAPTION_ICON[targetDocument.str_status]}
                  {targetDocument.str_status_name}
                </Flex>
              </Grid>
            </Flex>
            <Flex gap="sm">{employeeHeaderRightSlot}</Flex>
          </Flex>
          <TargetDocumentAccordion targetDocument={targetDocument} />
          <Flex justify="end" gap="sm">
            {actionsSlot}
          </Flex>
          {renderTable()}
        </StyledModalContent>
      </ModalV2>
    </>
  );
};
