import { ChevronDownIcon, ChevronUpIcon } from '@ghq-abi/design-system-icons';
import { useState } from 'react';
import styled from 'styled-components';
import { Flex, Grid } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { TargetDocumentDetailsObjectType } from '../TargetDocumentDetails/types';
import { TextLabel } from '../TextLabel/TextLabel';
import { TextDescription } from '../TextDescription/TextDescription';
import { AccordionChevron, AccordionContent, AccordionTrigger } from './styles';
import { TotalAchievementBadge } from '../TargetDocumentDetails/components/TotalAchievementBadge/TotalAchievementBadge';
import { FormatDate, standardCascadingStatuses } from '../../utils';
import { EntityValue } from './EntityValue';

const AccordionWrapper = styled.div`
  border: 1px solid #eeeff2;
  border-radius: 4px;
`;

export function TargetDocumentAccordion({
  targetDocument,
}: {
  targetDocument: TargetDocumentDetailsObjectType;
}) {
  const [expanded, setExpanded] = useState(true);
  const { t } = useTranslation();

  const isTotalAchivementVisible = ![
    ...standardCascadingStatuses,
    'REVIEW_MONITORING',
  ].includes(targetDocument.str_status);

  return (
    <AccordionWrapper>
      <AccordionTrigger onClick={() => setExpanded(!expanded)}>
        <Flex justify="between" css={{ width: '100%' }} align="center">
          <Flex align="center" gap="sm">
            <AccordionChevron>
              {expanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
            </AccordionChevron>
            <span>Target Document Details</span>
          </Flex>
          {isTotalAchivementVisible && <TotalAchievementBadge />}
        </Flex>
      </AccordionTrigger>
      <AccordionContent expanded={expanded}>
        <Grid
          css={{
            padding: '0.5rem 1rem 1.25rem 1rem',
            gridTemplateColumns: 'repeat(6, 1fr)',
            gridTemplateRows: 'repeat(2, 1fr)',
            rowGap: '1rem',
          }}
        >
          <Grid>
            <TextLabel>{t('common_employee')}</TextLabel>
            <TextDescription>
              {targetDocument.str_employee_name}
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_id')}</TextLabel>
            <TextDescription>
              {targetDocument.int_employee_global_id}
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_position')}</TextLabel>
            <TextDescription>
              {targetDocument.str_employee_positiontitle}
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_appraiser_name')}</TextLabel>
            <TextDescription>{targetDocument.str_manager_name}</TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_appraiser_id')}</TextLabel>
            <TextDescription>{targetDocument.int_manager_id}</TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_status')}</TextLabel>
            <TextDescription>{targetDocument.str_status_name}</TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_start_date')}</TextLabel>
            <TextDescription>
              {FormatDate(targetDocument.dat_start)}
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_end_date')}</TextLabel>
            <TextDescription>
              {FormatDate(targetDocument.dat_finish)}
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_time_dedication_ratio')}</TextLabel>
            <TextDescription>
              {targetDocument.int_time_dedication
                ? targetDocument.int_time_dedication
                : 0}
              %
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_bonus_schema')}</TextLabel>
            <TextDescription>
              {targetDocument.str_bonus_schema_name}
            </TextDescription>
          </Grid>
          <Grid>
            <TextLabel>{t('common_entity')}</TextLabel>
            <EntityValue targetDocumentId={targetDocument.uid} />
          </Grid>
          <Grid>
            <div>
              <TextLabel
                className="truncate-1"
                title={t('common_shared_target_owner')}
              >
                {t('common_shared_target_owner')}
              </TextLabel>
            </div>
            <TextDescription>
              {targetDocument.str_name_spoc || ''}
            </TextDescription>
          </Grid>
        </Grid>
      </AccordionContent>
    </AccordionWrapper>
  );
}
