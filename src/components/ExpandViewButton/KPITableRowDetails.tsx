import { Grid } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { AccordionContent } from './styles';
import { TargetDocumentDetailsKPIType } from '../TargetDocumentDetails/types';
import { TextDescription } from '../TextDescription/TextDescription';
import { TextLabel } from '../TextLabel/TextLabel';

export const KPITableRowDetails = ({
  kpi,
  expanded,
}: {
  kpi: TargetDocumentDetailsKPIType;
  expanded: boolean;
}) => {
  const { t } = useTranslation();
  return (
    <AccordionContent
      expanded={expanded}
      style={{
        background: '#FBFAFC',
        marginBottom: expanded ? '10px' : '0px',
      }}
    >
      <Grid
        gap="md"
        css={{
          padding: '0.5rem 1rem 1.25rem 1rem',
          gridTemplateColumns: '1fr 1fr 1fr 4fr',
        }}
      >
        <Grid>
          <TextLabel>{t('kpi_business_function')}</TextLabel>
          <TextDescription>{kpi.str_business_function_name}</TextDescription>
        </Grid>
        <Grid>
          <TextLabel>{t('kpi_type')}</TextLabel>
          <TextDescription>{kpi.str_kpi_type}</TextDescription>
        </Grid>
        <Grid>
          <TextLabel>{t('common_source')}</TextLabel>
          <TextDescription>{kpi.str_source_name}</TextDescription>
        </Grid>
        <Grid>
          <TextLabel>{t('kpi_calculation_method')}</TextLabel>
          <TextDescription>{kpi.str_calculation_method}</TextDescription>
        </Grid>
      </Grid>
    </AccordionContent>
  );
};
