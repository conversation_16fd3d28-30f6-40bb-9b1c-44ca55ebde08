import { Flex, Tooltip } from '@ghq-abi/design-system';
import { useEffect, useState } from 'react';
import { InfoCircleFillIcon } from '@ghq-abi/design-system-icons';
import { message } from 'antd';
import { PerformanceEntityContent } from '../PerformanceEntityContent';
import { PerformanceEntity } from '../PerformanceEntityTooltip/types';
import { TextDescription } from '../TextDescription/TextDescription';
import { listPerformanceEntityByTargetDocument } from '../../services/targetDocument/performanceEntityService';

export const EntityValue = ({
  targetDocumentId,
}: {
  targetDocumentId: string;
}) => {
  const [performanceEntities, setPerformanceEntities] =
    useState<Array<PerformanceEntity> | null>(null);

  const contentTooltipEntity = (): any => {
    return <PerformanceEntityContent performanceEntity={performanceEntities} />;
  };

  useEffect(() => {
    listPerformanceEntityByTargetDocument(targetDocumentId).then(res => {
      if (res.success) {
        setPerformanceEntities(res.data);
      } else {
        message.error(res.errorMessage, 1);
      }
    });
  }, []);

  return (
    <Flex align="center" gap="sm">
      <TextDescription>{performanceEntities?.[0]?.str_name}</TextDescription>
      <Tooltip align="center" content={contentTooltipEntity()} side="right">
        <Flex>
          <InfoCircleFillIcon width={14} height={14} />
        </Flex>
      </Tooltip>
    </Flex>
  );
};
