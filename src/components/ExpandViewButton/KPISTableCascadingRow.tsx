import { useState } from 'react';
import { Flex, Grid, Text } from '@ghq-abi/design-system';
import { Input } from 'antd';
import { ChevronDownIcon, ChevronUpIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';
import SelectV2 from '../selectV2/SelectV2';
import { ValueField } from '../KPIValueField/ValueField';
import {
  getPaValueByValueAndKpiType,
  isPAValueDisabled,
} from '../../businessRules';
import { KpiTypeUuids } from '../../utils';
import { AccordionChevron } from './styles';
import { TargetDocumentDetailsKPIType } from '../TargetDocumentDetails/types';
import targetDocumentService from '../../services/targetDocument/targetDocumentService';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPIAssignButton } from '../TargetDocumentDetails/components/DetailsContent/CascadingAccordionItemForm/KPIAssignButton';
import { KPIDeleteButton } from '../TargetDocumentDetails/components/DetailsContent/CascadingAccordionItemForm/KPIDeleteButton';
import { KPITableRowDetails } from './KPITableRowDetails';

export const KPISTableCascadingRow = ({
  kpi,
  index,
  full_scope,
}: {
  kpi: TargetDocumentDetailsKPIType;
  index: number;
  full_scope?: boolean;
}) => {
  const [expanded, setExpanded] = useState(false);
  const {
    setKpis,
    editMode,
    kpis,
    canDeleteKPIs,
    targetDocument,
    onUpdate: onUpdateTargetDocument,
  } = useTargetDocumentDetails();
  const { t } = useTranslation();

  const onUpdate = (data: Partial<TargetDocumentDetailsKPIType>) => {
    setKpis(
      kpis.map((kpiItem, i) => {
        if (i === index) return { ...kpiItem, ...data };
        return kpiItem;
      }),
    );
  };

  return (
    <Grid css={{ borderBottom: '1px solid #EEEFF2' }}>
      <Grid
        align="center"
        gap="sm"
        css={{
          gridTemplateColumns: '2fr 1fr 3fr 1fr 3fr 125px',
          padding: '10px 16px',
        }}
        key={`kpi-${kpi.uid}-index`}
      >
        <Text>{kpi.str_name || kpi.str_kpi_name || ''}</Text>
        {editMode ? (
          <SelectV2
            showSearch
            placeholder={t('common_weight')}
            onClick={e => e.stopPropagation()}
            options={targetDocumentService.catalogWeightList()}
            value={`${kpi.int_weight || '0'}`}
            onChange={value => onUpdate({ int_weight: +(value as string) })}
          />
        ) : (
          <Text>{`${kpi.int_weight || 0} %`}</Text>
        )}

        {editMode ? (
          <Input
            size="large"
            value={kpi.str_pa_value}
            disabled={isPAValueDisabled(kpi)}
            onClick={e => e.stopPropagation()}
            onChange={e => onUpdate({ str_pa_value: e.target.value })}
          />
        ) : (
          <Text className="truncate-2">{kpi.str_pa_value || ''}</Text>
        )}
        {editMode ? (
          <ValueField
            onChange={val =>
              onUpdate({
                str_value: `${val}`,
                str_pa_value: getPaValueByValueAndKpiType(
                  `${val}`,
                  kpi.uid_type as KpiTypeUuids,
                  kpi.str_pa_value,
                ),
              })
            }
            value={kpi.str_value}
            selectedKPI={kpi}
            onClick={e => e.stopPropagation()}
          />
        ) : (
          <Text className="truncate-2">{kpi.str_value || ''}</Text>
        )}
        {editMode ? (
          <Input
            size="large"
            value={kpi.str_scope}
            onClick={e => e.stopPropagation()}
            onChange={e => onUpdate({ str_scope: e.target.value })}
          />
        ) : (
          <Text>{kpi.str_scope || ''}</Text>
        )}
        <Flex justify="end">
          <KPIAssignButton
            kpi={kpi}
            full_scope={full_scope}
            targetDocument={targetDocument}
            onUpdate={(data, targetDocumentId) =>
              targetDocument.uid === targetDocumentId &&
              onUpdateTargetDocument(data)
            }
          />
          {canDeleteKPIs && (
            <KPIDeleteButton
              kpi={kpi}
              onDelete={() => {
                const newKpis = kpis.filter((_, i) => i !== index);
                onUpdateTargetDocument({ obj_target_document_kpis: newKpis });
                setKpis(newKpis);
              }}
              targetDocument={targetDocument}
            />
          )}
          <AccordionChevron
            style={{ padding: '5px', cursor: 'pointer' }}
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
          </AccordionChevron>
        </Flex>
      </Grid>
      <KPITableRowDetails expanded={expanded} kpi={kpi} />
    </Grid>
  );
};
