import { Grid } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPISTableMonitoringMonthlyRow } from './KPISTableMonitoringMonthlyRow';
import { StyledTextBold } from './styles';

export const KPISTableMonitoringMonthly = () => {
  const { kpis } = useTargetDocumentDetails();
  const { t } = useTranslation();

  return (
    <Grid>
      <Grid
        gap="sm"
        css={{
          gridTemplateColumns: '2fr 1fr 3fr 1fr 3fr 1fr 1fr 50px',
          padding: '10px 16px',
          paddingRight: '22px',
          borderBottom: '1px solid #EEEFF2',
          borderTop: '1px solid #CACDD5',
        }}
      >
        <StyledTextBold>{t('common_target_name')}</StyledTextBold>
        <StyledTextBold>{t('common_weight')}</StyledTextBold>
        <StyledTextBold>{t('common_pa_value')}</StyledTextBold>
        <StyledTextBold>{t('common_value')}</StyledTextBold>
        <StyledTextBold>{t('common_scope')}</StyledTextBold>
        <StyledTextBold>{t('common_year_to_date_abv')}</StyledTextBold>
        <StyledTextBold>{t('common_le')}</StyledTextBold>
      </Grid>
      <div style={{ overflowY: 'scroll', maxHeight: 'calc(100vh - 600px)' }}>
        {kpis.map(kpi => (
          <KPISTableMonitoringMonthlyRow
            kpi={kpi}
            key={`kpi-${kpi.uid}-index`}
          />
        ))}
      </div>
    </Grid>
  );
};
