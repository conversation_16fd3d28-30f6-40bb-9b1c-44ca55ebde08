import { Grid } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPISTableMonitoringAnnualRow } from './KPISTableMonitoringAnnualRow';
import { StyledTextBold } from './styles';
import { monthsNames } from '../../utils/monthsNames';

export const KPISTableMonitoringAnnual = () => {
  const { kpis } = useTargetDocumentDetails();
  const { t } = useTranslation();

  return (
    <Grid>
      <Grid
        gap="sm"
        css={{
          gridTemplateColumns: '4fr repeat(13, 1fr)',
          padding: '10px 16px',
          paddingRight: '22px',
          borderBottom: '1px solid #EEEFF2',
          borderTop: '1px solid #CACDD5',
        }}
      >
        <StyledTextBold>{t('common_target_name')}</StyledTextBold>
        <span />
        {monthsNames.map(monthName => (
          <StyledTextBold key={monthName.complete}>
            {t(monthName.short)}
          </StyledTextBold>
        ))}
      </Grid>
      <div style={{ overflowY: 'scroll', maxHeight: 'calc(100vh - 600px)' }}>
        {kpis.map(kpi => (
          <KPISTableMonitoringAnnualRow
            kpi={kpi}
            key={`kpi-${kpi.uid}-index`}
          />
        ))}
      </div>
    </Grid>
  );
};
