import { Flex, Grid, Text } from '@ghq-abi/design-system';
import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@ghq-abi/design-system-icons';
import { TargetDocumentDetailsKPIType } from '../TargetDocumentDetails/types';
import { AccordionChevron } from './styles';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPITableRowDetails } from './KPITableRowDetails';
import {
  getTrafficLight,
  TrafficLightPercentageMap,
} from '../TargetDocumentDetails/components/DetailsContent/AnnualView';

export const KPISTableMonitoringMonthlyRow = ({
  kpi,
}: {
  kpi: TargetDocumentDetailsKPIType;
}) => {
  const [expanded, setExpanded] = useState(false);
  const { month } = useTargetDocumentDetails();
  const objMonitoring = kpi.obj_monitoring?.find(
    obj => new Date(obj.dat_month_year).getMonth() + 1 === month,
  );
  return (
    <Grid css={{ borderBottom: '1px solid #EEEFF2' }}>
      <Grid
        align="center"
        css={{
          gridTemplateColumns: '2fr 1fr 3fr 1fr 3fr 1fr 1fr 50px',
          padding: '10px 16px',
          gap: '10px',
        }}
        key={`kpi-${kpi.uid}-index`}
      >
        <Text>{kpi.str_name || kpi.str_kpi_name || ''}</Text>
        <Text>{`${kpi.int_weight || 0} %`}</Text>
        <Text className="truncate-2">{kpi.str_pa_value || ''}</Text>
        <Text className="truncate-2">{kpi.str_value || ''}</Text>
        <Text>{kpi.str_scope || ''}</Text>
        <Flex align="center" gap="sm">
          {getTrafficLight(objMonitoring?.int_value ?? null, {
            marginBottom: '-0.125rem',
          })}
          {objMonitoring?.int_value !== null &&
          objMonitoring?.int_value !== undefined
            ? TrafficLightPercentageMap[objMonitoring.int_value]
            : '-'}
        </Flex>
        <Flex align="center" gap="sm">
          {getTrafficLight(objMonitoring?.int_value_le ?? null, {
            marginBottom: '-0.125rem',
          })}
          {objMonitoring?.int_value_le !== null &&
          objMonitoring?.int_value_le !== undefined
            ? TrafficLightPercentageMap[objMonitoring.int_value_le]
            : '-'}
        </Flex>
        <Flex justify="end">
          <AccordionChevron
            style={{ padding: '5px', cursor: 'pointer' }}
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
          </AccordionChevron>
        </Flex>
      </Grid>
      <KPITableRowDetails expanded={expanded} kpi={kpi} />
    </Grid>
  );
};
