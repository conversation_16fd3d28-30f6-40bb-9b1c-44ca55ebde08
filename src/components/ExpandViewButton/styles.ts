import { config, IconButtonV2, Text } from '@ghq-abi/design-system';
import styled from 'styled-components';

export const StyledIconButton = styled(IconButtonV2)`
  svg {
    width: 18px !important;
    height: 14px !important;
  }
`;

export const StyledModalContent = styled.div`
  display: grid;
  background: white;
  padding: ${config.theme.space[4]};
  gap: ${config.theme.space[4]};
`;

export const StyledTextBold = styled(Text)`
  font-weight: 600;
  color: #3f465a;
`;

export const AccordionTrigger = styled.button<{ alignment?: string }>`
  width: 100%;
  display: flex;
  justify-content: ${props => props.alignment || 'start'};
  align-items: center;
  gap: 10px;
  padding: 1.25rem 1rem;
  background-color: transparent;
  border: none;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f9f9f9;
  }

  span {
    font-weight: 600;
    font-size: 1.125rem;
  }
`;

export const AccordionChevron = styled.div`
  display: flex;
  background: transparent;
  justify-content: right;
  align-items: center;
  border: none;
  svg {
    width: 1.25rem;
    height: 1.25rem;
    padding: 0.25rem;
    border-radius: 100%;
    background-color: #b2c3cb;
  }
`;

export const AccordionContent = styled.div<{ expanded?: boolean }>`
  overflow: hidden;
  transition: all 0.25s ease-in-out;
  padding-right: 1rem;
  max-height: ${props => (props.expanded ? '100vh' : '0px')};
`;
