import { Grid } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPISTableCascadingRow } from './KPISTableCascadingRow';
import { StyledTextBold } from './styles';

export const KPISTableCascading = ({
  full_scope,
}: {
  full_scope?: boolean;
}) => {
  const { kpis } = useTargetDocumentDetails();
  const { t } = useTranslation();

  return (
    <Grid>
      <Grid
        gap="sm"
        css={{
          gridTemplateColumns: '2fr 1fr 3fr 1fr 3fr 125px',
          padding: '10px 16px',
          paddingRight: '22px',
          borderBottom: '1px solid #EEEFF2',
          borderTop: '1px solid #CACDD5',
        }}
      >
        <StyledTextBold>{t('common_target_name')}</StyledTextBold>
        <StyledTextBold>{t('common_weight')}</StyledTextBold>
        <StyledTextBold>{t('common_pa_value')}</StyledTextBold>
        <StyledTextBold>{t('common_value')}</StyledTextBold>
        <StyledTextBold>{t('common_scope')}</StyledTextBold>
      </Grid>
      <div style={{ overflowY: 'scroll', maxHeight: 'calc(100vh - 600px)' }}>
        {kpis.map((kpi, index) => (
          <KPISTableCascadingRow
            full_scope={full_scope}
            kpi={kpi}
            index={index}
            key={`kpi-${kpi.uid}-index`}
          />
        ))}
      </div>
    </Grid>
  );
};
