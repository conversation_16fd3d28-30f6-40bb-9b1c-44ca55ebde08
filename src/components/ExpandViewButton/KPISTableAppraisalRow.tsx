import { useState } from 'react';
import { Flex, Grid, Text } from '@ghq-abi/design-system';
import { Input } from 'antd';
import { ChevronDownIcon, ChevronUpIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';
import SelectV2 from '../selectV2/SelectV2';
import { AccordionChevron } from './styles';
import { TargetDocumentDetailsKPIType } from '../TargetDocumentDetails/types';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPITableRowDetails } from './KPITableRowDetails';

export const KPISTableAppraisalRow = ({
  kpi,
  index,
}: {
  kpi: TargetDocumentDetailsKPIType;
  index: number;
}) => {
  const [expanded, setExpanded] = useState(false);
  const { setKpis, kpis, editMode } = useTargetDocumentDetails();
  const { t } = useTranslation();

  const onUpdate = (data: Partial<TargetDocumentDetailsKPIType>) => {
    setKpis(
      kpis.map((kpiItem, i) => {
        if (i === index) return { ...kpiItem, ...data };
        return kpiItem;
      }),
    );
  };

  return (
    <Grid css={{ borderBottom: '1px solid #EEEFF2' }}>
      <Grid
        align="center"
        gap="md"
        css={{
          gridTemplateColumns: '2fr 1fr 2fr 1fr 2fr 2fr 50px',
          padding: '10px 16px',
        }}
        key={`kpi-${kpi.uid}-index`}
      >
        <Text>{kpi.str_name || kpi.str_kpi_name || ''}</Text>
        <Text>{`${kpi.int_weight || 0} %`}</Text>
        <Text>{kpi.str_scope || ''}</Text>

        {editMode ? (
          <Input
            size="large"
            value={kpi.str_final_value ?? ''}
            onChange={e => onUpdate({ str_final_value: e.target.value })}
          />
        ) : (
          <Text className="truncate-2">{kpi.str_final_value ?? ''}</Text>
        )}
        {editMode ? (
          <SelectV2
            showSearch
            placeholder={t('common_kpi_final_appraisal')}
            onClick={e => e.stopPropagation()}
            options={[
              { label: 0, value: 0 },
              { label: 80, value: 80 },
              { label: 90, value: 90 },
              { label: 100, value: 100 },
            ]}
            value={kpi.flt_final_appraisal}
            onChange={value =>
              onUpdate({ flt_final_appraisal: +(value as string) })
            }
          />
        ) : (
          <Text className="truncate-2">
            {kpi.flt_final_appraisal !== null &&
            kpi.flt_final_appraisal !== undefined
              ? `${kpi.flt_final_appraisal} %`
              : ''}
          </Text>
        )}
        <Text>{kpi.int_kpi_achievement ?? ''}</Text>

        <Flex justify="end">
          <AccordionChevron
            style={{ padding: '5px', cursor: 'pointer' }}
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
          </AccordionChevron>
        </Flex>
      </Grid>
      <KPITableRowDetails expanded={expanded} kpi={kpi} />
    </Grid>
  );
};
