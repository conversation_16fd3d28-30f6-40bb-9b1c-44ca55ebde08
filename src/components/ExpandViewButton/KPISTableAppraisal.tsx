import { Grid } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useTargetDocumentDetails } from '../TargetDocumentDetails/hooks/useTargetDocumentDetails';
import { KPISTableAppraisalRow } from './KPISTableAppraisalRow';
import { StyledTextBold } from './styles';

export const KPISTableAppraisal = () => {
  const { targetDocument } = useTargetDocumentDetails();
  const { t } = useTranslation();

  return (
    <Grid>
      <Grid
        gap="sm"
        css={{
          gridTemplateColumns: '2fr 1fr 2fr 1fr 2fr 2fr 50px',
          padding: '10px 16px',
          paddingRight: '22px',
          borderBottom: '1px solid #EEEFF2',
          borderTop: '1px solid #CACDD5',
        }}
      >
        <StyledTextBold>{t('common_target_name')}</StyledTextBold>
        <StyledTextBold>{t('common_weight')}</StyledTextBold>
        <StyledTextBold>{t('common_scope')}</StyledTextBold>
        <StyledTextBold>{t('common_final_value')}</StyledTextBold>
        <StyledTextBold>{t('common_kpi_final_appraisal')}</StyledTextBold>
        <StyledTextBold>{t('common_kpi_final_achievement')}</StyledTextBold>
      </Grid>
      <div style={{ overflowY: 'scroll', maxHeight: 'calc(100vh - 600px)' }}>
        {targetDocument.obj_target_document_kpis.map((kpi, index) => (
          <KPISTableAppraisalRow
            kpi={kpi}
            index={index}
            // eslint-disable-next-line react/no-array-index-key
            key={`kpi-${kpi.uid}-${index}`}
          />
        ))}
      </div>
    </Grid>
  );
};
