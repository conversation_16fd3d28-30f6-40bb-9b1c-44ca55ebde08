import {
  Dropdown,
  Flex,
  IconButton,
  Text,
  styled,
} from '@ghq-abi/design-system';

export const StyledIconButton = styled(IconButton, {
  border: 'none',
  padding: '$1-5',

  '&:hover:not(:disabled)': {
    backgroundColor: '#191F2E14',
    borderRadius: '100px',
  },
});

export const StyledDropdownContent = styled(Dropdown.Content, {
  marginTop: '0px !important',
  zIndex: '$overlay',
  borderColor: '#EEEFF2',
  position: 'relative',
  boxShadow: '8px 0px 16px 0px #191F2E3D',
  width: '100vw',
  '@md': { width: 'fit-content' },
});

export const StyledText = styled(Text, {
  fontWeight: '$medium',
  color: '#191F2E',
});

export const StyledFlex = styled(Flex, {
  borderBottom: '1px solid #EEEFF2',
  height: '$12',
  padding: '$2 $4',
});

export const StyledDropdownItem = styled(Dropdown.Item, {
  px: '$md',
  py: '$sm',
  '&:focus:not(:hover)': {
    boxShadow: '0 0 0 2px $colors$black inset',
  },
});

export const StyledDropdownFlex = styled(Flex, {
  marginTop: '0px !important',
});

export const StyledSubtitle = styled(Text, {
  fontWeight: '$normal',
  color: '#7D8597',
  fontSize: '$0-5',
});

export const StyledTitle = styled(Text, {
  fontWeight: '$normal',
  color: '#191F2E',
});
