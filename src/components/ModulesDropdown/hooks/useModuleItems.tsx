import { ModuleDropdownProps } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import configs from '../../../services/configs';
import {
  ClipboardIcon,
  MoneyBagIcon,
  TalentCardIcon,
  UnionIcon,
} from '../../icons';
import { useUser } from '../../../context/User';

export function useModuleItems() {
  const { user } = useUser();

  const canShowCheershubModule = () => {
    return (
      configs.module_url_cheershub &&
      !configs.module_url_cheershub.includes('CHEERSHUB_URL') &&
      configs.cheershub_zones &&
      !configs.cheershub_zones.includes('CHEERSHUB_ZONES') &&
      configs.cheershub_zones.includes(user.uid_zone)
    );
  };

  const canShowLcmModule = () =>
    configs.module_url_lcm && !configs.module_url_lcm.includes('LCM_URL');

  const canShowMyCompensationModule = () =>
    configs.show_my_compensation_module === 'true';
  const { t } = useTranslation();
  const items: ModuleDropdownProps['items'] = [];

  if (canShowCheershubModule())
    items.push({
      label: t('common_my_cheershub'),
      subtitle: t('common_cheershub'),
      icon: <UnionIcon fill="#191F2E" />,
      url: configs.module_url_cheershub,
    });
  if (canShowLcmModule())
    items.push({
      label: t('common_my_180_360'),
      subtitle: t('common_lcm'),
      icon: <ClipboardIcon stroke="#191F2E" />,
      url: configs.module_url_lcm,
    });

  if (canShowMyCompensationModule())
    items.push({
      label: t('common_my_bonus'),
      subtitle: t('common_compensations'),
      icon: <MoneyBagIcon fill="#191F2E" />,
      url: `${configs.module_url_rewards}bonus-simulator`,
    });

  items.push({
    label: t('common_my_talent_card'),
    subtitle: t('common_opr'),
    icon: <TalentCardIcon fill="#191F2E" />,
    url: configs.quick_access_url_my_talent_card,
  });

  return { items };
}
