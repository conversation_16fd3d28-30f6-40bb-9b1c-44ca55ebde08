import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleDropdown } from '@ghq-abi/design-system';

import { useModuleItems } from './hooks/useModuleItems';

export function ModulesDropdown() {
  const { items } = useModuleItems();
  const { t } = useTranslation();

  return (
    <ModuleDropdown
      title={t('common_quick_access')}
      items={items}
      offset={16}
    />
  );
}
