import React from 'react';
import { Box, Button } from '@ghq-abi/design-system';

import styles from './styles.module.scss';

type FloatingButtonProps = {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  disabled?: boolean;
};

export const FloatingButton: React.FC<FloatingButtonProps> = ({
  icon,
  label,
  onClick,
  disabled = false,
}) => {
  return (
    <Box className={styles.floatingButtonBox}>
      <Button
        disabled={disabled}
        className={styles.floatingButton}
        leftIcon={icon}
        onClick={onClick}
      >
        {label}
      </Button>
    </Box>
  );
};
