import { message, Popconfirm } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonV2 } from '@ghq-abi/design-system';
import { SendIcon } from '@ghq-abi/design-system-icons';
import appraisalService from '../../services/appraisal/appraisalService';

export const AppraisalSendChangesButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (newData: {
    str_status: string;
    str_status_name: string;
    bol_can_edit: number;
  }) => void;
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const onConfirm = () => {
    setLoading(true);
    appraisalService
      .appraisalSubmit({ uid_target_document: targetDocument.uid })
      .then(res => {
        if (res.success) {
          onUpdate({
            str_status: 'APPRAISAL_APPRAISAL_COMPLETE',
            str_status_name: t('appraisal_appraisal_complete'),
            bol_can_edit: 0,
          });
          message.success(t('message_target_document_approved'));
        } else {
          message.error(res.errorMessage);
        }
      })
      .finally(() => setLoading(false));
  };
  return (
    <Popconfirm
      title={t('message_you_have_sure')}
      onConfirm={onConfirm}
      okText={t('common_yes')}
      cancelText={t('common_no')}
    >
      <ButtonV2 loading={loading} size="md" rightIcon={<SendIcon />}>
        {t('common_complete_appraisal')}
      </ButtonV2>
    </Popconfirm>
  );
};
