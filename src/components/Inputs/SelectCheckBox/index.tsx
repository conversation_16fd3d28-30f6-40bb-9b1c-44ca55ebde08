import { MultipleSelectProps, MultipleSelect } from '@ghq-abi/design-system';
import { CSSObject } from 'styled-components';
import { useId } from 'react';
import { useTranslation } from 'react-i18next';
import * as S from './styles';

interface IOptions {
  value: any;
  label?: string | undefined;
}

interface ISelectCheckBoxProps extends MultipleSelectProps {
  options: IOptions[];
  sxContainer?: CSSObject;
  sxInput?: CSSObject;
}

export const InputSelectCheckBox = ({
  sxContainer,
  sxInput,
  ...rest
}: ISelectCheckBoxProps) => {
  const { t } = useTranslation();
  const id = useId();

  const customStyle: any = {
    container: (provided: any) => ({
      ...provided,
      ...sxContainer,
      fontFamily: 'OpenSans, Arial, Helvetica, sans-serif !important',
    }),
    input: (provided: any) => ({
      ...provided,
      ...sxInput,
      fontSize: '1rem',
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: '#bfbfbf !important',
      fontWeight: '400 !important',
      fontSize: '1rem',
    }),
    control: (provided: any) => ({
      ...provided,
      ...sxInput,
      fontFamily: 'OpenSans, Arial, Helvetica, sans-serif !important',
      fontSize: '1rem',
      padding: '0px 12px !important',
      boxShadow: 'none',
      borderRadius: '4px',
    }),
    multiValue: (provided: any) => ({
      ...provided,
      background: '#CDE3FA',
    }),
    clearIndicator: (provided: any) => ({
      ...provided,
      cursor: 'pointer',
    }),
    indicatorsContainer: (provided: any) => ({
      ...provided,
      cursor: 'pointer',
    }),
  };

  return (
    <S.Container sxContainer={sxContainer} sxInput={sxInput}>
      <MultipleSelect
        id={id}
        styles={customStyle}
        placeholder={rest.placeholder ?? t('common_select_option')}
        {...rest}
      />
    </S.Container>
  );
};
