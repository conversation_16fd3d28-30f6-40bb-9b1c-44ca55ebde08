import { XNewLgIcon } from '@ghq-abi/design-system-icons';
import { ReactNode } from 'react';

export const TableFloatingSelection = ({
  absoluteBottom,
  actionButtons,
  selectedsSize,
  clearSelecteds,
  selectedsText,
}: {
  absoluteBottom?: boolean;
  actionButtons: ReactNode | ReactNode[];
  selectedsSize: number;
  clearSelecteds?: () => void;
  selectedsText: string;
}) => {
  return (
    <div
      style={{
        position: absoluteBottom ? 'absolute' : 'unset',
        bottom: '70px',
        display: 'flex',
        justifyContent: 'space-between',
        width: '-webkit-fill-available',
        marginTop: '10px',
        borderRadius: '200px',
        padding: '16px 24px',
        border: '1px solid #EEEFF2',
        background: '#FBFAFC',
        boxShadow: '-4px 4px 20px 0px rgba(0,0,0,0.08)',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
        {clearSelecteds && (
          <XNewLgIcon style={{ cursor: 'pointer' }} onClick={clearSelecteds} />
        )}
        <div
          style={{
            width: '24px',
            height: '24px',
            background: '#325A6D',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            borderRadius: '100%',
          }}
        >
          {selectedsSize}
        </div>
        <span>{selectedsText}</span>
      </div>
      <div style={{ display: 'flex', gap: '10px' }}>{actionButtons}</div>
    </div>
  );
};
