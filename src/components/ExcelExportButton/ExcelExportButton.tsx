import { IconButtonV2, Text, Tooltip } from '@ghq-abi/design-system';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { ExcelExportIcon } from '../icons';
import reportService from '../../services/report/reportService';
import { downloadFile } from '../../utils';
import { GeneratedFullSanityFilters } from '../../services/report/types';

export const ExcelExportButton = ({
  filters,
}: {
  filters: GeneratedFullSanityFilters;
}) => {
  const { t } = useTranslation();
  const handleExport = async () => {
    try {
      message.loading(
        { content: t('message_action_progress'), key: 'update' },
        0,
      );

      // @todo: go back to async after some testes, fixing email, changing notification to websocket with sse
      // await reportService.generateScheduler(ReportType.FULLSANITY, {
      //   ...filters,
      //   bol_teamreport: true,
      // });
      // message.success({
      //   content: t('message_reporting_async'),
      //   key: 'update',
      //   duration: 3,
      // });

      const res = await reportService.generateFullSanity(filters);
      downloadFile({ file: res.data.file });
      message.success({ content: t('common_success'), key: 'update' }, 0.5);
    } catch (e) {
      message.error(
        { content: (e as Error).message || t('request_error'), key: 'update' },
        1,
      );
    }
  };

  return (
    <Tooltip
      content={
        (
          <Text
            css={{
              color: '$white',
              fontSize: '0.75rem',
              fontWeight: '$bold',
            }}
            dangerouslySetInnerHTML={{
              __html: t('common_export_full_sanity'),
            }}
          />
        ) as any
      }
      side="top"
    >
      <IconButtonV2
        size="md"
        variant="ghost"
        rounded
        overrideSvgColor={false}
        icon={<ExcelExportIcon />}
        onClick={handleExport}
      />
    </Tooltip>
  );
};
