export type EntityTarget = {
  str_name: string;
  dat_start?: string;
  dat_end?: string;
  int_weight: number;
};

export type PerformanceEntity = {
  uid: Array<string>;
  id_performance_entity_target?: number;
  str_name: string;
  str_score?: number;
  dat_start?: string;
  dat_end?: string;
  uid_upper_entity?: string;
  str_upper_entity_name?: string;
  int_upper_weight?: number;
  uid_closest_entity?: string;
  str_closest_entity_name?: string;
  int_closest_weight?: number;
  uid_macro_entity?: string;
  str_macro_entity_name?: string;
  obj_entities: Array<EntityTarget>;
};
