import React, { useState } from 'react';
import { message, Spin, Tooltip } from 'antd';

import { InfoIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';
import { Flex, Text } from '@ghq-abi/design-system';
import { listPerformanceEntityByTargetDocument } from '../../services/targetDocument/performanceEntityService';
import { PerformanceEntityContent } from '../PerformanceEntityContent';
import { PerformanceEntity } from './types';

type PerformanceEntityTooltip = {
  uidTargetDocument: string;
  hideLabel?: boolean;
};

export const PerformanceEntityTooltip: React.FC<PerformanceEntityTooltip> = ({
  uidTargetDocument,
  hideLabel,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);

  const [performanceEntities, setPerformanceEntities] =
    useState<Array<PerformanceEntity> | null>(null);

  const onTrigger = (visible: boolean) => {
    if (!visible || performanceEntities) {
      return;
    }

    setLoading(true);

    listPerformanceEntityByTargetDocument(uidTargetDocument)
      .then(res => {
        if (res.success) {
          setPerformanceEntities(res.data);
        } else {
          message.error(res.errorMessage, 1);
        }
      })
      .finally(() => setLoading(false));
  };

  return (
    <Tooltip
      title={
        loading ? (
          <Spin
            style={{ display: 'flex', alignItems: 'center', margin: '5px' }}
          />
        ) : (
          <PerformanceEntityContent performanceEntity={performanceEntities} />
        )
      }
      placement="topLeft"
      trigger={['hover', 'click']}
      onVisibleChange={onTrigger}
    >
      <Flex
        align="center"
        gap="sm"
        css={{
          fontWeight: '$bold',
          color: '#191f2e',
          svg: {
            width: '16px',
            height: '16px',
          },
        }}
      >
        {!hideLabel && <Text>{`${t('common_entity')}:`}</Text>}
        <InfoIcon />
      </Flex>
    </Tooltip>
  );
};
