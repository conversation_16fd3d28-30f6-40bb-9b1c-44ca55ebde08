import { useState } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { Text, Tooltip } from '@ghq-abi/design-system';
import { FileEarmarkArrowDownIcon } from '@ghq-abi/design-system-icons';
import { downloadFile } from '../../utils';
import { generateTargetDocumentAppraisalTemplate } from '../../services/report/reportService';
import { StyledIconButton } from './styles';

export const AppraisalTemplateButton = ({
  targetDocumentIds,
  year,
}: {
  targetDocumentIds: string[];
  year: number;
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const onExportAppraisalTemplate = () => {
    setLoading(true);
    generateTargetDocumentAppraisalTemplate({
      arr_td_uids: targetDocumentIds,
      arr_years: year,
    })
      .then(response => {
        if (!response.success || !response.data)
          throw new Error(response.errorMessage);
        message.success({ content: t('common_success') }, 1);
        downloadFile({
          file: response.data.file,
          report: true,
          target: 'blank',
        });
      })
      .catch(error => message.error({ content: error.message }, 20))
      .finally(() => setLoading(false));
  };
  return (
    <Tooltip
      content={
        (
          <Text
            css={{
              color: '$white',
              fontSize: '0.75rem',
              fontWeight: '$bold',
            }}
            dangerouslySetInnerHTML={{
              __html: t('download_appraisal_template'),
            }}
          />
        ) as any
      }
      side="top"
    >
      <StyledIconButton
        size="md"
        loading={loading}
        rounded
        onClick={onExportAppraisalTemplate}
        icon={<FileEarmarkArrowDownIcon />}
        variant="ghost"
        showActiveOutline={false}
      />
    </Tooltip>
  );
};
