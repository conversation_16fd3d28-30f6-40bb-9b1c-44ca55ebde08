import { ButtonV2 } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { message, Tooltip } from 'antd';
import { IDataKpi } from '../../services/cascading/types';
import cascadingService from '../../services/cascading/cascadingService';
import { TargetDocumentBlockerPopConfirm } from '../TargetDocumentBlocker/TargetDocumentBlockerPopConfirm';

export const CascadingSendToEmployeeButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    uid_employee: string;
    obj_target_document_kpis: Array<{
      int_weight?: number;
      str_scope?: string;
    }>;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (newData: {
    str_status: string;
    str_status_name: string;
    bol_can_edit: number;
  }) => void;
}) => {
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleSendToEmployee = () => {
    setLoading(true);
    cascadingService
      .sendTargetDocumentToEmployeeApp(targetDocument.uid)
      .then(res => {
        if (res.success) {
          message.success(
            { content: t('message_sent_employee_approval'), key: 'update' },
            1,
          );
          onUpdate({ ...res.data, bol_can_edit: 0 });
        } else {
          message.error({ content: res.errorMessage, key: 'update' }, 1);
        }
      })
      .finally(() => setLoading(false));
  };

  const items = targetDocument.obj_target_document_kpis;

  const filteredItems = items.filter(
    (item): item is IDataKpi => 'int_weight' in item && 'str_scope' in item,
  );

  const isValid = filteredItems.reduce(
    (acc, item) => {
      acc.totalWeight += item.int_weight;
      acc.hasEmptyScope = acc.hasEmptyScope || item.str_scope === '';
      return acc;
    },
    { totalWeight: 0, hasEmptyScope: false },
  );

  const showTooltip = isValid.totalWeight === 100 && !isValid.hasEmptyScope;

  return (
    <TargetDocumentBlockerPopConfirm
      targetDocument={targetDocument}
      title={t('message_send_target_document_to_employee_approval')}
      onConfirm={handleSendToEmployee}
      okText={t('common_yes')}
      cancelText={t('common_no')}
    >
      <ButtonV2 loading={loading} size="md" disabled={!showTooltip}>
        {!showTooltip ? (
          <Tooltip
            placement="topLeft"
            title={
              <>
                {isValid.totalWeight < 100 &&
                  t('common_alert_send_to_employee_weight')}
                <br />
                {isValid.hasEmptyScope &&
                  t('common_alert_send_to_employee_scope')}
              </>
            }
          >
            {t('common_send_to_employee')}
          </Tooltip>
        ) : (
          t('common_send_to_employee')
        )}
      </ButtonV2>
    </TargetDocumentBlockerPopConfirm>
  );
};
