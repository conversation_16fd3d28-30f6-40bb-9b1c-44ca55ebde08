import React from 'react';
import { message, Space } from 'antd';
import { useTranslation } from 'react-i18next';

import { ButtonV2, Dialog } from '@ghq-abi/design-system';
import { InfoIcon, SquarePIcon } from '@ghq-abi/design-system-icons';
import { downloadFile } from '../../utils';

import { generateTargetDocumentAppraisalTemplate } from '../../services/report/reportService';
import { ActionItem } from '../actionItem';

type ButtonExportAppraisalTemplateProps = {
  targetDocumentUids: string[];
  employeeIds?: number[];
  year?: number;
  size?: 'sm' | 'md' | 'lg';
  isActionModalItem?: boolean;
};

export const ButtonExportAppraisalTemplate: React.FC<
  ButtonExportAppraisalTemplateProps
> = ({
  targetDocumentUids,
  employeeIds,
  year,
  size = 'md',
  isActionModalItem,
}) => {
  const { t } = useTranslation();

  const onExportAppraisalTemplate = () => {
    message.loading({
      content: t('message_action_progress'),
      key: 'report-generation',
      duration: 10,
    });

    generateTargetDocumentAppraisalTemplate({
      arr_td_uids: targetDocumentUids,
      arr_years: year,
      arr_employee_ids: employeeIds,
    })
      .then(response => {
        if (!response.success || !response.data) {
          throw new Error(response.errorMessage);
        }

        message.success(
          {
            content: t('common_success'),
            key: 'report-generation',
          },
          1,
        );

        downloadFile({
          file: response.data.file,
          report: true,
          target: 'blank',
        });
      })
      .catch(error =>
        message.error(
          {
            content: error.message,
            key: 'report-generation',
          },
          20,
        ),
      );
  };

  if (isActionModalItem) {
    return (
      <Dialog.Close asChild>
        <ActionItem
          variant="borderless"
          size="sm"
          onClick={onExportAppraisalTemplate}
          leftIcon={<SquarePIcon width="16px" height="16px" />}
        >
          {t('common_export_appraisal_template')}
        </ActionItem>
      </Dialog.Close>
    );
  }

  return (
    <ButtonV2
      size={size}
      variant="secondary"
      leftIcon={<InfoIcon />}
      onClick={onExportAppraisalTemplate}
    >
      <Space>{t('download_appraisal_template')}</Space>
    </ButtonV2>
  );
};
