import { ButtonV2, config } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { useState } from 'react';
import TextArea from 'antd/lib/input/TextArea';
import { ChatAlertIcon, SendIcon } from '@ghq-abi/design-system-icons';
import { useTargetDocumentBlocker } from '../TargetDocumentBlocker/useTargetDocumentBlocker';
import cascadingService from '../../services/cascading/cascadingService';
import ModalV2 from '../modalV2/ModalV2';

export const CascadingRejectButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
    str_status: string;
  };
  onUpdate: (newData: {
    str_status: string;
    str_status_name: string;
    str_reject_reason: string | null;
    bol_can_edit: number;
  }) => void;
}) => {
  const { openWarning } = useTargetDocumentBlocker(targetDocument);
  const [loading, setLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [reason, setReason] = useState('');
  const { t } = useTranslation();

  const handleReject = () => {
    setLoading(true);
    cascadingService
      .rejectTargetDocument(targetDocument.uid, reason)
      .then(res => {
        if (res.success) {
          message.success(
            { content: t('message_target_document_rejected'), key: 'update' },
            1,
          );
          onUpdate({
            ...res.data,
            str_reject_reason: reason,
            bol_rejected_cascading: 1,
          });
          setModalOpen(false);
        } else {
          message.error({ content: res.errorMessage, key: 'update' }, 1);
        }
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      <ButtonV2
        size="md"
        loading={loading}
        variant="secondary"
        leftIcon={<ChatAlertIcon width={20} height={20} />}
        style={{
          borderColor: '#FF3236',
          color: '#FF3236',
        }}
        onClick={() => {
          if (!openWarning()) setModalOpen(true);
        }}
      >
        {t(
          targetDocument.str_status === 'CASCADING_EMPLOYEE_APPROVAL'
            ? 'common_give_feedback_employee'
            : 'common_give_feedback',
        )}
      </ButtonV2>
      <ModalV2
        open={modalOpen}
        title={t(
          targetDocument.str_status === 'CASCADING_EMPLOYEE_APPROVAL'
            ? 'common_give_feedback_employee'
            : 'common_give_feedback',
        )}
        hasFooter={false}
        onCancel={() => setModalOpen(false)}
      >
        <div>
          <div
            style={{ padding: '20px', background: config.theme.colors.white }}
          >
            <TextArea
              style={{
                height: '200px',
                background: config.theme.colors.gray50,
              }}
              minLength={5}
              value={reason}
              onChange={e => setReason(e.target.value)}
            />
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '8px',
              marginTop: '16px',
            }}
          >
            <ButtonV2
              size="md"
              variant="secondary"
              onClick={() => setModalOpen(false)}
            >
              {t('common_cancel')}
            </ButtonV2>
            <ButtonV2
              loading={loading}
              size="md"
              leftIcon={<SendIcon />}
              onClick={handleReject}
            >
              {t('common_send_feedback')}
            </ButtonV2>
          </div>
        </div>
      </ModalV2>
    </>
  );
};
