import React from 'react';
import { Col, Row, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { ICustomExpandeRow } from './interface';
import { Container, Label } from '..';
import { useUser } from '../../context/User';

const ExpandRow: React.FC<ICustomExpandeRow> = ({ data }) => {
  const { employeeOrAppraiser } = useUser();
  const { t } = useTranslation();

  return (
    <Container transparent padding="0px" data-testid="expandeRow-test">
      <Row>
        <Col span={5}>
          <Space direction="vertical">
            {data.str_kpi_id && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('kpi_id')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>{data.str_kpi_id}</Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
            {!employeeOrAppraiser && data.str_business_function_name && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('kpi_business_function')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>{data.str_business_function_name}</Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
            {data.str_kpi_type && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('kpi_type')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>{data.str_kpi_type}</Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
            {data.str_source_name && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('common_source')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>{data.str_source_name}</Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
          </Space>
        </Col>
        <Col span={19}>
          <Space direction="vertical">
            {data.str_pa_value && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('common_pa_value')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>{data.str_pa_value}</Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
            {data.str_help_note && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('common_comment')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>{data.str_help_note}</Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
            {data.str_calculation_method && (
              <Row>
                <Col>
                  <Row>
                    <Col>
                      <Label bold>{`${t('common_calculation_method')}:`}</Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Label>
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'left',
                            justifyContent: 'center',
                          }}
                        >
                          {data.str_calculation_method.split(';').map(item => (
                            <div>{item}</div>
                          ))}
                        </div>
                      </Label>
                    </Col>
                  </Row>
                </Col>
              </Row>
            )}
          </Space>
        </Col>
      </Row>
    </Container>
  );
};

export default ExpandRow;
