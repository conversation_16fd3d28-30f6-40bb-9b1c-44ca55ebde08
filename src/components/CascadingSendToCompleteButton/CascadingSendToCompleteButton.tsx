import { ButtonV2 } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { message } from 'antd';
import { SendIcon } from '@ghq-abi/design-system-icons';
import settingService from '../../services/setting/settingService';
import { TargetDocumentBlockerPopConfirm } from '../TargetDocumentBlocker/TargetDocumentBlockerPopConfirm';

export const CascadingSendToCompleteButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    uid_employee: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (newData: {
    str_status: string;
    str_status_name: string;
    str_target_document_status_name: string;
  }) => void;
}) => {
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleSend = () => {
    setLoading(true);
    settingService
      .sendTargetDocumentToCompleteStatus({
        uid_employee: targetDocument.uid_employee,
        uid: targetDocument.uid,
      })
      .then(res => {
        if (res.success) {
          message.success(
            { content: t('message_setting_complete_status'), key: 'update' },
            1,
          );
          onUpdate({
            str_status: 'PREPARATION_SETTING_COMPLETE',
            str_status_name: 'Preparation - Setting Complete',
            str_target_document_status_name: 'Preparation - Setting Complete',
          });
        } else {
          message.error({ content: res.errorMessage, key: 'update' }, 1);
        }
      })
      .finally(() => setLoading(false));
  };

  return (
    <TargetDocumentBlockerPopConfirm
      targetDocument={targetDocument}
      title={t('message_send_to_complete')}
      onConfirm={handleSend}
      okText={t('common_yes')}
      cancelText={t('common_no')}
    >
      <ButtonV2 leftIcon={<SendIcon />} loading={loading} size="md">
        {t('common_send')}
      </ButtonV2>
    </TargetDocumentBlockerPopConfirm>
  );
};
