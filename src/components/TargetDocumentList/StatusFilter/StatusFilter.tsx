import { useTranslation } from 'react-i18next';
import { TreeNode } from 'antd/lib/tree-select';
import { Dispatch, SetStateAction } from 'react';
import { useTargetDocumentFilterData } from '../hooks/useTargetDocumentFilterData';
import { FilterLabel, FilterWrapper } from '../../filterLabel';
import { StyledTreeSelect } from './styles';
import { TargetDocumentListFiltersObjectType } from '../types';

export const StatusFilter = ({
  tempFilters,
  setTempFilters,
  standardStatuses,
}: {
  tempFilters: Partial<TargetDocumentListFiltersObjectType>;
  setTempFilters: Dispatch<SetStateAction<TargetDocumentListFiltersObjectType>>;
  standardStatuses: string[];
}) => {
  const { t } = useTranslation();
  const { statusTreeOptions } = useTargetDocumentFilterData();

  const getTreeNodes = (itemTree: (typeof statusTreeOptions)[0]) => {
    if (itemTree.children) {
      return (
        <TreeNode
          key={itemTree.key || itemTree.value}
          value={itemTree.value}
          title={itemTree.label}
        >
          {itemTree.children.map(itemSubtree =>
            getTreeNodes(itemSubtree as any),
          )}
        </TreeNode>
      );
    }
    return (
      <TreeNode
        key={itemTree.value}
        value={itemTree.value}
        title={itemTree.label}
      />
    );
  };

  return (
    <FilterWrapper>
      <FilterLabel>{t('common_status')}</FilterLabel>
      <StyledTreeSelect
        getPopupContainer={trigger => trigger.parentNode}
        size="large"
        value={tempFilters?.arr_status}
        showSearch
        treeDefaultExpandAll
        dropdownStyle={{
          maxHeight: 400,
          overflow: 'auto',
        }}
        placeholder={t('common_select_option')}
        allowClear
        multiple
        maxTagCount={1}
        onClear={() =>
          setTempFilters({ ...tempFilters, arr_status: standardStatuses })
        }
        onChange={(value: any) => {
          setTempFilters(prev => ({
            ...prev,
            arr_status: value.length ? value : standardStatuses,
          }));
        }}
      >
        {statusTreeOptions?.map(itemTree => getTreeNodes(itemTree))}
      </StyledTreeSelect>
    </FilterWrapper>
  );
};
