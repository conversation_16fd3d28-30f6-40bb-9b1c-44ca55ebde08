import { useTranslation } from 'react-i18next';
import { TreeNode } from 'antd/lib/tree-select';
import { useTargetDocumentFilterData } from '../hooks/useTargetDocumentFilterData';
import { FilterLabel, FilterWrapper } from '../../filterLabel';
import { StyledTreeSelect } from './styles';
import { TargetDocumentListFiltersObjectType } from '../types';
import { TargetDocumentListItem } from '../../../services/targetDocument/types';

export const StatusFilterForGridView = ({
  filters,
  refresh,
  standardStatuses,
}: {
  filters: TargetDocumentListFiltersObjectType;
  refresh: (
    data: Partial<TargetDocumentListFiltersObjectType>,
    callback?: (targetDocuments: TargetDocumentListItem[]) => void,
    delay?: number,
  ) => void;
  standardStatuses: string[];
}) => {
  const { t } = useTranslation();
  const { statusTreeOptions } = useTargetDocumentFilterData();

  const getTreeNodes = (itemTree: (typeof statusTreeOptions)[0]) => {
    if (itemTree.children) {
      return (
        <TreeNode
          key={itemTree.key || itemTree.value}
          value={itemTree.value}
          title={itemTree.label}
        >
          {itemTree.children.map(itemSubtree =>
            getTreeNodes(itemSubtree as any),
          )}
        </TreeNode>
      );
    }
    return (
      <TreeNode
        key={itemTree.value}
        value={itemTree.value}
        title={itemTree.label}
      />
    );
  };

  return (
    <FilterWrapper>
      <FilterLabel>{t('common_status')}</FilterLabel>
      <StyledTreeSelect
        getPopupContainer={trigger => trigger.parentNode}
        size="large"
        value={filters?.arr_status}
        showSearch
        treeDefaultExpandAll
        dropdownStyle={{
          maxHeight: 400,
          overflow: 'auto',
        }}
        placeholder={t('common_select_option')}
        allowClear
        multiple
        maxTagCount={1}
        onClear={() => refresh({ arr_status: [] }, undefined, 0)}
        onChange={(value: any) =>
          refresh({ arr_status: value.length ? value : standardStatuses })
        }
      >
        {statusTreeOptions?.map(itemTree => getTreeNodes(itemTree))}
      </StyledTreeSelect>
    </FilterWrapper>
  );
};
