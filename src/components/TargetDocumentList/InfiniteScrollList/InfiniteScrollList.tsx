import { Flex } from '@ghq-abi/design-system';
import { Empty } from 'antd';
import React, { PropsWithChildren } from 'react';
import { SpinStandard } from '../../spinStandard';
import { StyledInfiniteScroll } from './styles';

export const InfiniteScrollList: React.FC<
  PropsWithChildren & {
    loading: boolean;
    hasData: boolean;
    hasMoreData: boolean;
    onLoadMore: () => void;
  }
> = ({ children, loading, hasData, hasMoreData, onLoadMore }) => {
  return (
    <div style={{ overflow: 'auto' }}>
      {!loading && !hasData && (
        <Flex justify="center">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </Flex>
      )}
      <StyledInfiniteScroll
        initialLoad={false}
        loadMore={onLoadMore}
        hasMore={!loading && hasMoreData}
        useWindow={false}
        threshold={20}
      >
        {children}
        {loading && <SpinStandard />}
      </StyledInfiniteScroll>
    </div>
  );
};
