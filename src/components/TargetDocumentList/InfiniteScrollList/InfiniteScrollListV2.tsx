import { Flex } from '@ghq-abi/design-system';
import { Empty } from 'antd';
import React, { PropsWithChildren } from 'react';
import { SpinStandard } from '../../spinStandard';
import { StyledInfiniteScroll } from './styles';

// duplicated for now to prevent breaking things like KpiCatalog that are not part of this task and needs to be well tested before making this one the "official" version
export const InfiniteScrollListV2: React.FC<
  PropsWithChildren & {
    loading: boolean;
    hasData: boolean;
    hasMoreData: boolean;
    onLoadMore: (page: number) => void;
    page: number;
    hideNoData?: boolean;
  }
> = ({
  children,
  loading,
  hasData,
  hasMoreData,
  onLoadMore,
  page,
  hideNoData,
}) => {
  return (
    <div style={{ overflow: 'auto' }}>
      {!hideNoData && !loading && !hasData && (
        <Flex justify="center">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </Flex>
      )}
      <StyledInfiniteScroll
        initialLoad={false}
        pageStart={1}
        pageToLoad={page}
        loadMore={onLoadMore}
        hasMore={!loading && hasMoreData}
        useWindow={false}
        threshold={20}
      >
        {children}
        {loading && <SpinStandard />}
      </StyledInfiniteScroll>
    </div>
  );
};
