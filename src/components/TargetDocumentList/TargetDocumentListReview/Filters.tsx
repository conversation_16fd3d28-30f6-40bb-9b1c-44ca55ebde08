import { Flex, IconButtonV2 } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { useEffect, useMemo, useState } from 'react';
import lodash from 'lodash';
import { useLocation } from 'react-router-dom';
import { SearchIcon } from '@ghq-abi/design-system-icons';
import {
  HiddenFilters,
  HiddenFiltersButton,
  HiddenFiltersProvider,
} from '../../hiddenFilters';
import Search from '../../search/Search';
import { useTargetDocumentFilterData } from '../hooks/useTargetDocumentFilterData';
import { useUser } from '../../../context/User';
import { InsertIdsButton } from '../../InsertIdsButton/InsertIdsButton';
import { AdvancedFilter } from '../../Filters/AdvancedFilter';
import { TargetDocumentListFiltersObjectType } from '../types';
import { standardReviewStatuses } from '../../../utils';
import { TargetDocumentFilters } from './TargetDocumentFilters';
import { useTargetDocumentList } from '../hooks/useTargetDocumentList';
import { TargetDocumentListItem } from '../../../services/targetDocument/types';

const FILTER_KEYS = [
  'str_employee_position',
  'arr_status',
  'arr_display',
  'arr_slt_level',
  'arr_uid_bonus_schema',
  'arr_macroentity',
  'arr_supervisory_org',
  'arr_managers_lvl_1',
  'arr_managers_lvl_slt',
  'arr_management_chain',
  'str_manager_name_id',
  'str_function',
  'str_function_abi_entity',
  'str_zone_id',
  'str_org_name_id',
];

export const Filters = ({
  filters,
  refresh,
  updateSelected,
}: {
  filters: TargetDocumentListFiltersObjectType;
  refresh: (
    data?: Partial<TargetDocumentListFiltersObjectType>,
    callback?: (targetDocuments: TargetDocumentListItem[]) => void,
    page?: number,
    delay?: number,
  ) => void;
  updateSelected?: (targetDocuments: TargetDocumentListItem[]) => void;
}) => {
  const { t } = useTranslation();
  const { initialFilters } = useTargetDocumentList();
  const { pathname } = useLocation();
  const isPathMyTeam = pathname.includes('my-team');
  const { user } = useUser();
  const [, setSelDisplay] = useState([user.uuid]);
  const [shouldApplyFilters, setShouldApplyFilters] = useState(false);
  const {
    displayOptions: displayOptionsData,
    dataFilters,
    setDataFilters,
  } = useTargetDocumentFilterData();

  const [tempFilters, setTempFilters] = useState(filters);

  const displayOptions = displayOptionsData?.map(opt => {
    return {
      label: opt.str_display_name,
      value: opt.uid_employee,
    };
  });

  const handleResetFilters = () => {
    setTempFilters(initialFilters);
  };

  const isResetButtonDisabled = useMemo(
    () =>
      !FILTER_KEYS.some(key =>
        Array.isArray(tempFilters[key])
          ? !!tempFilters[key].length
          : !!tempFilters[key],
      ),
    [tempFilters],
  );

  const handleApplyFilters = () => {
    refresh(tempFilters, updateSelected, 1, 0);
  };

  const isApplyButtonDisabled = lodash.isEqual(filters, tempFilters);

  const handleInputChange = (name: string, value: any) => {
    if (name === 'array_display' && value === true) {
      const option: { label: string; value: string }[] = displayOptions?.filter(
        opt => opt.label === 'My Direct Reports',
      );
      setSelDisplay([option[0].value]);
      setTempFilters(prevValues => ({
        ...prevValues,
        arr_display: [option[0].value],
      }));
    } else if (name === 'array_display' && value === false) {
      const options: string[] = displayOptions?.map(opt => opt.value);
      setSelDisplay(options);
      setTempFilters(prevValues => ({
        ...prevValues,
        arr_display: options,
      }));
    } else if (Array.isArray(value) && value.length > 0) {
      setTempFilters(prevValues => ({
        ...prevValues,
        [name]: value,
      }));
    } else if (!Array.isArray(value) && value) {
      // empty array is truthy, so we need to garantee that it's not an empty array
      setTempFilters(prevValues => ({
        ...prevValues,
        [name]: ['str_manager_name_id', 'str_employee_name_id'].includes(name)
          ? value
          : [value],
      }));
    } else {
      setTempFilters(prevValues => ({
        ...prevValues,
        [name]: undefined,
      }));
    }
  };

  useEffect(() => {
    if (isPathMyTeam && shouldApplyFilters) {
      handleApplyFilters();
      setShouldApplyFilters(false);
    }
  }, [shouldApplyFilters, tempFilters]);

  useEffect(() => {
    if (isPathMyTeam && tempFilters.str_employee_name_id === '') {
      handleResetFilters();
    }

    if (!isPathMyTeam && tempFilters.str_employee_name_id === '') {
      handleApplyFilters();
    }

    if (
      !isPathMyTeam &&
      shouldApplyFilters &&
      tempFilters.str_employee_name_id !== ''
    ) {
      handleApplyFilters();
      setShouldApplyFilters(false);
    }
  }, [tempFilters.str_employee_name_id, isPathMyTeam, shouldApplyFilters]);

  const handleKeyDown = e => {
    if (e.key === 'Enter') {
      setShouldApplyFilters(true);
    }
  };

  return (
    <HiddenFiltersProvider>
      <Flex gap="sm">
        <Search
          allowClear
          wrapperStyles={{ width: '100%' }}
          name="str_employee_name_id"
          placeholder={t('common_search_by_name_id')}
          value={tempFilters?.str_employee_name_id}
          onChange={e =>
            setTempFilters(prev => ({
              ...prev,
              str_employee_name_id: e.target.value,
            }))
          }
          onKeyDown={handleKeyDown}
        />
        <InsertIdsButton
          searchValue={tempFilters.str_employee_name_id ?? ''}
          onUpdateSearch={newValue =>
            setTempFilters(prev => ({
              ...prev,
              str_employee_name_id: newValue,
            }))
          }
          key={tempFilters.str_employee_name_id}
        />
        <HiddenFiltersButton />
        <IconButtonV2
          onClick={handleApplyFilters}
          variant="primary"
          size="md"
          icon={<SearchIcon />}
        />
      </Flex>
      {isPathMyTeam ? (
        <HiddenFilters greyBackground align="bottom">
          <Flex
            direction="column"
            css={{
              padding: '1rem',
              width: '100%',
            }}
            gap="sm"
          >
            <TargetDocumentFilters
              tempFilters={tempFilters}
              setTempFilters={setTempFilters}
              clearFilters={handleResetFilters}
              handleApplyFilters={handleApplyFilters}
              isApplyButtonDisabled={isApplyButtonDisabled}
            />
          </Flex>
        </HiddenFilters>
      ) : (
        <AdvancedFilter
          handleResetFilters={handleResetFilters}
          isResetButtonDisabled={isResetButtonDisabled}
          handleApplyFilters={handleApplyFilters}
          isApplyButtonDisabled={isApplyButtonDisabled}
          handleInputChange={handleInputChange}
          tempFilters={tempFilters}
          setTempFilters={setTempFilters}
          filters={filters}
          standardStatuses={standardReviewStatuses}
          dataFilters={dataFilters}
          setDataFilters={setDataFilters}
        />
      )}
    </HiddenFiltersProvider>
  );
};
