import {
  ApprovalIcon,
  CompletedIcon,
  InProgressIcon,
  NotStartedIcon,
  TrackAndMonitorIcon,
} from './icons';
import { PreAssignationOrAppraisalIcon } from './icons/PreAssignationOrAppraisalIcon';
import { TargetDocumentListFiltersObjectType } from './types';

export const STATUS_CAPTION_NAME_ICON = {
  'In Progress': <InProgressIcon />,
  'Not Started': <NotStartedIcon />,
  'Appraiser Approval': <ApprovalIcon />,
  'Employee Approval': <ApprovalIcon />,
  Approval: <ApprovalIcon />,
  Completed: <CompletedIcon />,
  'Track and Monitor': <TrackAndMonitorIcon />,
  'Pre-Assignation': <PreAssignationOrAppraisalIcon />,
  'Pre-Appraisal': <PreAssignationOrAppraisalIcon />,
};

export const STATUS_CAPTION_ICON = {
  CASCADING_EMPLOYEE_APPROVAL: <ApprovalIcon />,
  CASCADING_APPRAISER_APPROVAL: <ApprovalIcon />,
  PREPARATION_SETTING_COMPLETE: <NotStartedIcon />,
  CASCADING_NOT_STARTED: <NotStartedIcon />,
  CASCADING_MANAGER_EDITING: <InProgressIcon />,
  CASCADING_COMPLETED: <CompletedIcon />,
  FEEDBACK_GIVEN: <ApprovalIcon />,
  PREPARATION_SETTING: <NotStartedIcon />,
  CASCADING_PRE_ASSIGNATION: <PreAssignationOrAppraisalIcon />,

  REVIEW_MONITORING: <TrackAndMonitorIcon />,
  APPRAISAL_NOT_STARTED: <NotStartedIcon />,
  APPRAISAL_PRE_APPRAISAL: <PreAssignationOrAppraisalIcon />,
  APPRAISAL_EMPLOYEE_IN_PROGRESS: <InProgressIcon />,
  APPRAISAL_EMPLOYEE_APPROVAL: <ApprovalIcon />,
  APPRAISAL_APPRAISER_IN_PROGRESS: <InProgressIcon />,
  APPRAISAL_APPRAISER_APPROVAL: <ApprovalIcon />,
  APPRAISAL_APPRAISAL_COMPLETE: <CompletedIcon />,
  COMPLETED: <CompletedIcon />,
};

export const STATUS_NAME = {
  APPROVAL: 'Approval',
  CASCADING_EMPLOYEE_APPROVAL: 'Employee Approval',
  CASCADING_APPRAISER_APPROVAL: 'Appraiser Approval',
  PREPARATION_SETTING_COMPLETE: 'Not Started',
  CASCADING_NOT_STARTED: 'Not Started',
  CASCADING_MANAGER_EDITING: 'In Progress',
  CASCADING_COMPLETED: 'Completed',
  FEEDBACK_GIVEN: 'Feedback Given',
  SHARED_TARGET: 'Shared Target',
  PREPARATION_SETTING: 'Not Started',
  CASCADING_PRE_ASSIGNATION: 'Pre-Assignation',

  REVIEW_MONITORING: 'Track and Monitor',
  APPRAISAL_NOT_STARTED: 'Not Started',
  APPRAISAL_PRE_APPRAISAL: 'Pre-Appraisal',
  APPRAISAL_EMPLOYEE_IN_PROGRESS: 'In Progress',
  APPRAISAL_EMPLOYEE_APPROVAL: 'Employee Approval',
  APPRAISAL_APPRAISER_IN_PROGRESS: 'In Progress',
  APPRAISAL_APPRAISER_APPROVAL: 'Appraiser Approval',
  APPRAISAL_APPRAISAL_COMPLETE: 'Completed',
  COMPLETED: 'Completed',
} as const;

export const STATUS_COLOR = {
  'Track and Monitor': '#2561ED',
  'Not Started': '#7D8597',
  'In Progress': '#F2DC39',
  Approval: '#FF3236',
  'Employee Approval': '#FF3236',
  'Appraiser Approval': '#FF3236',
  Completed: '#44AC21',
  'Pre-Assignation': '#8247E5',
  'Pre-Appraisal': '#8247E5',
};

export const STATUS_COLOR_BG = {
  'Track and Monitor': '#EBF3FF',
  'Not Started': '#F5F6F7',
  'In Progress': '#FFFCEB',
  Approval: '#FFF8C8',
  'Employee Approval': '#FFF0E9',
  'Appraiser Approval': '#FFF0E9',
  Completed: '#F6FFEA',
  'Pre-Assignation': '#F4F1FF',
  'Pre-Appraisal': '#F4F1FF',
};

export const STATUS_COLOR_BG_HOVER = {
  'Track and Monitor': '#D3E5FE',
  'Not Started': '#EEEFF2',
  'In Progress': '#FBF2B3',
  Approval: '#FFE3D6',
  'Employee Approval': '#FFE3D6',
  'Appraiser Approval': '#FFE3D6',
  Completed: '#EBFCD5',
  'Pre-Assignation': '#D5CCFF',
  'Pre-Appraisal': '#D5CCFF',
};

export const CURRENT_STATUS_LABELS: (typeof STATUS_NAME)[keyof typeof STATUS_NAME][] =
  [
    'Track and Monitor',
    'Pre-Appraisal',
    'Not Started',
    'In Progress',
    'Approval',
    'Completed',
  ];

export const CASCADING_STATUS_LABELS: (typeof STATUS_NAME)[keyof typeof STATUS_NAME][] =
  ['Not Started', 'Pre-Assignation', 'In Progress', 'Approval', 'Completed'];

export const INITIAL_FILTERS: TargetDocumentListFiltersObjectType = {
  str_employee_name_id: undefined,
  str_manager_name_id: undefined,
  arr_status: undefined,
  arr_display: undefined,
  str_zone_id: undefined,
  arr_slt_level: undefined,
  str_employee_position: undefined,
  str_function: undefined,
  str_function_abi_entity: undefined,
  str_function_abi_entity_2: undefined,
  str_org_name_id: undefined,
  arr_macroentity: undefined,
  arr_supervisory_org: undefined,
  arr_management_chain: undefined,
  arr_uid_bonus_schema: undefined,
  arr_managers_lvl_1: undefined,
  arr_managers_lvl_2: undefined,
  arr_managers_lvl_slt: undefined,
  str_target_document_status: undefined,
  uid_bonus_schema: undefined,
};
