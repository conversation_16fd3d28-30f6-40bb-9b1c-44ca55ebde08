import { Box, config, Label, Tooltip } from '@ghq-abi/design-system';
import styled from 'styled-components';
import {
  STATUS_CAPTION_NAME_ICON,
  STATUS_COLOR,
  STATUS_NAME,
} from '../constants';

const StyledStatusCaptionTooltipContent = styled.div`
  color: white;
  display: flex;
  align-items: center;
  gap: ${config.theme.space[2]};
`;

export const TargetDocumentStatusCaption = ({
  status = 'Not Started',
  count = 0,
}: {
  status: (typeof STATUS_NAME)[keyof typeof STATUS_NAME];
  count: number;
}) => {
  return (
    <Tooltip
      align="center"
      avoidCollisions
      content={
        (
          <StyledStatusCaptionTooltipContent>
            {status}
            {STATUS_CAPTION_NAME_ICON[status]}
          </StyledStatusCaptionTooltipContent>
        ) as any
      }
      side="bottom"
    >
      <Box
        css={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minWidth: '40px',
          padding: '0 4px',
          height: '32px',
          backgroundColor: '$gray50',
          borderRadius: '0.25rem',
        }}
      >
        <Box
          css={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.37rem',
            justifyContent: 'center',
          }}
        >
          <Box
            css={{
              width: '9px',
              height: '9px',
              border: '1px solid $gray450',
              backgroundColor: STATUS_COLOR[status],
              borderRadius: '0.125rem',
            }}
          />
          <Label css={{ fontWeight: '$medium', fontSize: '0.75rem' }}>
            {count}
          </Label>
        </Box>
      </Box>
    </Tooltip>
  );
};
