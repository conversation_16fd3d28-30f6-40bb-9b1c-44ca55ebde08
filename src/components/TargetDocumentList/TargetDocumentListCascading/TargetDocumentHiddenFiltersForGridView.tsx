import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonV2, Flex, MultipleSelect, theme } from '@ghq-abi/design-system';
import styled from 'styled-components';
import { XNewLgIcon } from '@ghq-abi/design-system-icons';
import { FilterLabel } from '../../filterLabel';
import Search from '../../search/Search';
import { TargetDocumentListFiltersObjectType } from '../types';
import { useTargetDocumentFilterData } from '../hooks/useTargetDocumentFilterData';
import { standardCascadingStatuses } from '../../../utils';
import { useUser } from '../../../context/User';
import { StatusFilterForGridView } from '../StatusFilter/StatusFilterForGridView';
import { TargetDocumentListItem } from '../../../services/targetDocument/types';

const StyledMultiSelect = styled(MultipleSelect)`
  > div {
    height: 40px;
    padding: 8px 16px !important;
  }
`;

const FILTER_KEYS = ['str_employee_position', 'arr_status', 'arr_display'];

export const TargetDocumentHiddenFiltersForGridView = ({
  clearFilters,
  filters,
  refresh,
}: {
  filters: TargetDocumentListFiltersObjectType;
  refresh: (
    data: Partial<TargetDocumentListFiltersObjectType>,
    callback?: (targetDocuments: TargetDocumentListItem[]) => void,
    delay?: number,
  ) => void;
  clearFilters: () => void;
}) => {
  const { user } = useUser();
  const { t } = useTranslation();
  const [selDisplay, setSelDisplay] = useState([user.uuid]);

  const { displayOptions: displayOptionsData, loading: filterLoading } =
    useTargetDocumentFilterData();

  const displayOptions = displayOptionsData?.map(opt => {
    return {
      label: opt.str_display_name,
      value: opt.uid_employee,
    };
  });

  const isClearButtonDisabled = useMemo(
    () =>
      !FILTER_KEYS.some(key =>
        Array.isArray(filters[key]) ? !!filters[key].length : !!filters[key],
      ),
    [filters],
  );

  return (
    <>
      <Flex direction="column">
        <FilterLabel>{t('common_position')}</FilterLabel>
        <Search
          style={{
            fontSize: theme.fontSizes[2].value,
            width: '307px',
            minWidth: '100%',
            color: '#3F465A',
            height: '2.5rem',
          }}
          name="str_employee_position"
          value={filters?.str_employee_position}
          placeholder={t('common_search_by_name_id')}
          onChange={e => refresh({ str_employee_position: e.target.value })}
        />
      </Flex>
      <StatusFilterForGridView
        filters={filters}
        refresh={refresh}
        standardStatuses={standardCascadingStatuses}
      />
      <Flex align="start" direction="column" gap="sm">
        <FilterLabel>{t('common_display')}</FilterLabel>
        <StyledMultiSelect
          isLoading={filterLoading}
          placeholder={t('common_search_select_display')}
          value={displayOptions?.filter(opt => selDisplay.includes(opt.value))}
          onChange={newValues => {
            setSelDisplay(newValues.map(opt => opt.value));
            refresh(
              { arr_display: newValues.map(opt => opt.value) },
              undefined,
              0,
            );
          }}
          options={displayOptions}
        />
      </Flex>
      <div>
        <ButtonV2
          size="md"
          variant="secondary"
          leftIcon={<XNewLgIcon />}
          onClick={() => {
            setSelDisplay([user.uuid]);
            clearFilters();
          }}
          disabled={isClearButtonDisabled}
        >
          {t('common_clear_filters')}
        </ButtonV2>
      </div>
    </>
  );
};
