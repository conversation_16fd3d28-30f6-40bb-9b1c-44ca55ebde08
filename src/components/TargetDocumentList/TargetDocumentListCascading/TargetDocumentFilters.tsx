import { Dispatch, SetStateAction, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonV2, Flex, MultipleSelect } from '@ghq-abi/design-system';
import { XNewLgIcon } from '@ghq-abi/design-system-icons';
import { FilterLabel } from '../../filterLabel';
import { StatusFilter } from '../StatusFilter/StatusFilter';
import { TargetDocumentListFiltersObjectType } from '../types';
import { useTargetDocumentFilterData } from '../hooks/useTargetDocumentFilterData';
import { standardCascadingStatuses } from '../../../utils';
import { useUser } from '../../../context/User';

const FILTER_KEYS = ['str_employee_position', 'arr_status', 'arr_display'];

export const TargetDocumentFilters = ({
  tempFilters,
  setTempFilters,
  clearFilters,
  handleApplyFilters,
  isApplyButtonDisabled,
}: {
  tempFilters: TargetDocumentListFiltersObjectType;
  setTempFilters: Dispatch<SetStateAction<TargetDocumentListFiltersObjectType>>;
  clearFilters: () => void;
  handleApplyFilters: () => void;
  isApplyButtonDisabled: boolean;
}) => {
  const { user } = useUser();
  const { t } = useTranslation();
  const [selDisplay, setSelDisplay] = useState([user.uuid]);

  const { displayOptions: displayOptionsData, loading: filterLoading } =
    useTargetDocumentFilterData();

  const displayOptions = displayOptionsData?.map(opt => {
    return {
      label: opt.str_display_name,
      value: opt.uid_employee,
    };
  });

  const isClearButtonDisabled = useMemo(
    () =>
      !FILTER_KEYS.some(key =>
        Array.isArray(tempFilters[key])
          ? !!tempFilters[key].length
          : !!tempFilters[key],
      ),
    [tempFilters],
  );

  return (
    <>
      <StatusFilter
        tempFilters={tempFilters}
        setTempFilters={setTempFilters}
        standardStatuses={standardCascadingStatuses}
      />
      <Flex align="start" direction="column" gap="sm">
        <FilterLabel>{t('common_display')}</FilterLabel>
        <MultipleSelect
          isLoading={filterLoading}
          placeholder={t('common_search_select_display')}
          value={displayOptions?.filter(opt => selDisplay.includes(opt.value))}
          onChange={(newValues: any) => {
            setSelDisplay(newValues.map(opt => opt.value));
            setTempFilters(prev => ({
              ...prev,
              arr_display: newValues.map(opt => opt.value),
            }));
          }}
          options={displayOptions}
          hasSelectAll
        />
      </Flex>

      <Flex justify="between">
        <Flex gap="md">
          <ButtonV2
            size="md"
            variant="secondary"
            leftIcon={<XNewLgIcon />}
            onClick={() => {
              setSelDisplay([user.uuid]);
              clearFilters();
            }}
            disabled={isClearButtonDisabled}
          >
            {t('common_clear_filters')}
          </ButtonV2>
          <ButtonV2
            size="md"
            variant="primary"
            onClick={() => {
              handleApplyFilters();
            }}
            disabled={isApplyButtonDisabled}
          >
            {t('common_apply_filters')}
          </ButtonV2>
        </Flex>
      </Flex>
    </>
  );
};
