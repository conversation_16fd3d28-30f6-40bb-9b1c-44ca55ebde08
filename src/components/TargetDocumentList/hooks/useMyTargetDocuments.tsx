import {
  Dispatch,
  PropsWithChildren,
  SetStateAction,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';
import { TargetDocumentCount, TargetDocumentListFilters } from '../types';
import targetDocumentService from '../../../services/targetDocument/targetDocumentService';
import { TargetDocumentListItem } from '../../../services/targetDocument/types';

let fetchTimeout: NodeJS.Timeout;

const MyTargetDocumentsContext = createContext(
  {} as {
    targetDocuments: TargetDocumentListItem[];
    hasMoreData: boolean;
    loading: boolean;
    filters: TargetDocumentListFilters;
    refresh: (
      newFilters?: Partial<TargetDocumentListFilters>,
      callback?: (targetDocuments: TargetDocumentListItem[]) => void,
      page?: number,
      delay?: number,
    ) => void;
    onUpdate: (
      data: Partial<TargetDocumentListItem>,
      otherTargetDocumentUID?: string,
    ) => void;
    initialFilters: TargetDocumentListFilters;
    totalRows: TargetDocumentCount;
    setFilters: Dispatch<SetStateAction<TargetDocumentListFilters>>;
    pageIndex: number;
  },
);

export const MyTargetDocumentsProvider: React.FC<
  PropsWithChildren & {
    initialFilters: TargetDocumentListFilters;
  }
> = ({ children, initialFilters }) => {
  const [targetDocuments, setTargetDocuments] = useState<
    TargetDocumentListItem[]
  >([]);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalRows, setTotalRows] = useState({ total: 0 });
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] =
    useState<TargetDocumentListFilters>(initialFilters);
  const [pageIndex, setPageIndex] = useState<number>(1);

  const refresh = (
    filterData?: Partial<TargetDocumentListFilters>,
    callback?: (targetDocuments: TargetDocumentListItem[]) => void,
    page = 1,
    delay = 1000,
  ) => {
    if (loading) return;

    const newFilters = {
      ...filters,
      ...(filterData ?? {}),

      // pagination data
      int_pageindex: page,
      int_pagesize: 30,
    };

    if (newFilters.int_pageindex === 1) {
      setTargetDocuments([]);
    }

    setLoading(true);
    setFilters(newFilters);
    clearTimeout(fetchTimeout);
    fetchTimeout = setTimeout(async () => {
      const {
        data: { lines, total },
      } = await targetDocumentService.getMyTargetDocuments(newFilters);

      setTargetDocuments(lines);
      setHasMoreData(lines.length < total.total);
      setTotalRows(total);
      setPageIndex(page);
      setLoading(false);

      callback?.(lines);
    }, delay);
  };

  const onUpdate = (
    data: Partial<TargetDocumentListItem>,
    selectedUid?: string,
  ) => {
    setTargetDocuments(
      targetDocuments.map(td =>
        td.uid === selectedUid ? { ...td, ...data } : td,
      ),
    );
  };

  useEffect(() => {
    refresh();
  }, []);

  return (
    <MyTargetDocumentsContext.Provider
      // eslint-disable-next-line react/jsx-no-constructed-context-values
      value={{
        targetDocuments,
        loading,
        filters,
        hasMoreData,
        refresh,
        onUpdate,
        initialFilters,
        totalRows,
        setFilters,
        pageIndex,
      }}
    >
      {children}
    </MyTargetDocumentsContext.Provider>
  );
};

export const useMyTargetDocuments = () => {
  const context = useContext(MyTargetDocumentsContext);

  if (context === undefined) {
    throw new Error(
      'useMyTargetDocuments must be used within a MyTargetDocumentsProvider',
    );
  }
  return context;
};
