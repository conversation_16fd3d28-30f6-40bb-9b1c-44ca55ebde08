import {
  <PERSON><PERSON><PERSON>,
  Props<PERSON><PERSON><PERSON><PERSON>dren,
  SetStateAction,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { DefaultOptionType } from 'antd/lib/select';
import { useLocation } from 'react-router-dom';
import { useUser } from '../../../context/User';
import targetDocumentService from '../../../services/targetDocument/targetDocumentService';
import { TargetDocumentDropdownFilters } from '../../../services/targetDocument/types';
import performanceEntityService from '../../../services/performanceEntity/performanceEntityService';
import adminService from '../../../services/admin/adminService';

export interface IDataFilter {
  arr_functions?: DefaultOptionType[];
  arr_slt_level?: DefaultOptionType[];
  arr_zones?: DefaultOptionType[];
  arr_clusters?: DefaultOptionType[];
  arr_status?: DefaultOptionType[];
  arr_hierarchy?: any[];
  arr_abi_functions?: DefaultOptionType[];
  arr_abi_functions_2?: DefaultOptionType[];
  arr_uid_bonus_schema?: any[];
  arr_supervisory_org: any[];
  arr_managers_lvl_1?: any[];
  arr_managers_lvl_2?: any[];
  arr_managers_lvl_slt?: any[];
  arr_management_chain?: any[];
}

type StatusTreeOptionsType = Array<{
  key: string;
  value: string;
  label: string;
  children: Array<{
    key: string;
    value: string;
    label: string;
  }>;
}>;

type DisplayOptionsType = Array<{
  str_display_name: string;
  uid_employee: string;
}>;

const TargetDocumentFilterDataContext = createContext(
  {} as {
    displayOptions: DisplayOptionsType;
    statusTreeOptions: StatusTreeOptionsType;
    dataFilters: TargetDocumentDropdownFilters | undefined;
    setDataFilters: Dispatch<
      SetStateAction<TargetDocumentDropdownFilters | undefined>
    >;
    loading: boolean;
  },
);

export const TargetDocumentFilterDataProvider: React.FC<
  PropsWithChildren & { year?: number; statuses: string[]; all_abi?: boolean }
> = ({
  children,
  year = new Date().getFullYear(),
  statuses,
  all_abi = true,
}) => {
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [dataFilters, setDataFilters] =
    useState<TargetDocumentDropdownFilters>();
  const [statusTreeOptions, setStatusTreeOptions] =
    useState<StatusTreeOptionsType>([]);
  const [displayOptions, setDisplayOptions] = useState<DisplayOptionsType>([]);
  const { pathname } = useLocation();

  const providerValues = useMemo(
    () => ({
      displayOptions,
      statusTreeOptions,
      dataFilters,
      setDataFilters,
      loading,
    }),
    [displayOptions, statusTreeOptions, dataFilters, setDataFilters, loading],
  );

  const processTargetDocuments = (
    targetDocumentsFilters: TargetDocumentDropdownFilters,
  ) => {
    let arr_status: StatusTreeOptionsType = [];

    targetDocumentsFilters?.arr_td_status.forEach(element => {
      if (statuses.includes(element.str_status_id)) {
        const aux = element.str_status_name.split(' - ');
        if (
          !arr_status.find(
            item => item.value === aux[0].toString().toUpperCase(),
          )
        ) {
          arr_status.push({
            key: aux[0].toString().toUpperCase(),
            value: aux[0].toString().toUpperCase(),
            label: aux[0],
            children: [],
          });
        }

        arr_status = arr_status.map(status => {
          if (status.value === 'INACTIVE' || status.value === 'COMPLETED') {
            return status;
          }
          if (aux[1] && status.value === aux[0].toString().toUpperCase()) {
            status.children?.push({
              key: element.str_status_id,
              value: element.str_status_id,
              label: aux[1],
            });
          }
          return status;
        });
      }
    });

    if (pathname === '/my-team/trackmonitoring') {
      arr_status = arr_status.filter(status => status.key === 'REVIEW');
    } else if (pathname === '/my-team/appraisal') {
      arr_status = arr_status.filter(status => status.key === 'APPRAISAL');
    }

    if (pathname === '/my-scope/trackmonitoring') {
      arr_status = arr_status.filter(status => status.key === 'REVIEW');
    } else if (pathname === '/my-scope/appraisal') {
      arr_status = arr_status.filter(status => status.key === 'APPRAISAL');
    }

    setStatusTreeOptions(arr_status);
    setDisplayOptions([
      {
        str_display_name: 'My Direct Reports',
        uid_employee: user.uuid,
      },
      ...(targetDocumentsFilters?.arr_display.map(s => ({
        ...s,
        str_display_name: `${s.str_display_name} Reports`,
      })) || []),
    ]);
  };

  const PATHS_MY_TEAM = ['/my-team/cascading', '/my-team/current'];

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        let targetDocumentsFilters;
        let hierarchyFilters = [];
        let bonusSchemaFilters = [];
        let supervisoryOrgsHierarchyFilters = [];
        let managementChainHierarchyFilters = [];

        if (!PATHS_MY_TEAM.includes(pathname)) {
          const [
            listDropdownRes,
            costHierarchyRes,
            bonusSchemaRes,
            supervisoryOrgsRes,
            managementChainRes,
          ] = await Promise.all([
            targetDocumentService.listDropdown(year.toString(), all_abi),
            performanceEntityService.getCostCentersHierarchy(false),
            adminService.getBonusSchemasScope(),
            performanceEntityService.getSupervisoryOrgsHierarchy(),
            performanceEntityService.getManagementChainHierarchy(),
          ]);

          targetDocumentsFilters = listDropdownRes?.data || [];
          hierarchyFilters = costHierarchyRes?.data || [];
          bonusSchemaFilters = bonusSchemaRes?.data || [];
          supervisoryOrgsHierarchyFilters = supervisoryOrgsRes?.data || [];
          managementChainHierarchyFilters = managementChainRes?.data || [];
        } else {
          // If in PATHS_MY_TEAM, only fetch the necessary data
          const listDropdownRes = await targetDocumentService.listDropdown(
            year.toString(),
            all_abi,
          );
          targetDocumentsFilters = listDropdownRes?.data || {};
        }

        processTargetDocuments(targetDocumentsFilters);

        setDataFilters({
          ...targetDocumentsFilters,
          arr_uid_bonus_schema: bonusSchemaFilters,
          arr_hierarchy: hierarchyFilters,
          arr_supervisory_org: supervisoryOrgsHierarchyFilters,
          arr_management_chain: managementChainHierarchyFilters,
        });
      } catch (error) {
        if (process.env.REACT_APP_STAGE === 'development') {
          console.error('Error fetching data:', error);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [pathname]);

  return (
    <TargetDocumentFilterDataContext.Provider value={providerValues}>
      {children}
    </TargetDocumentFilterDataContext.Provider>
  );
};

export const useTargetDocumentFilterData = () => {
  const context = useContext(TargetDocumentFilterDataContext);

  if (context === undefined) {
    throw new Error(
      'useTargetDocumentFilterData must be used within a TargetDocumentFilterDataProvider',
    );
  }

  return context;
};
