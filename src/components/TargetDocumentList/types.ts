import { STATUS_NAME } from './constants';

export type TargetDocumentListObjectType = {
  uid: string;
  int_employee_global_id: number;
  str_status: string;
  str_employee_name: string;
  dat_start: string;
  dat_finish: string;
  global_id_spoc?: number | null;
  id_spoc?: number | null;
  str_name_spoc?: string | null;
  str_function_spoc?: string | null;
};

export type TargetDocumentListFiltersObjectType = {
  int_year?: number;
  int_month?: number;
  str_zone_id?: string;
  str_function?: string;
  str_function_abi_entity?: string;
  str_function_abi_entity_2?: string;
  str_org_name_id?: string;
  arr_macroentity?: string[];
  str_target_document_status?: string;
  uid_bonus_schema?: string;
  str_employee_name_id?: string;
  str_employee_position?: string;
  str_manager_name_id?: string;
  arr_status?: string[];
  arr_display?: string[];
  arr_functions?: string[];
  arr_slt_level?: string[];
  arr_zones?: string[];
  arr_abi_functions?: string[];
  arr_abi_functions_2?: string[];
  arr_hierarchy?: string[];
  arr_uid_bonus_schema?: string[];
  arr_supervisory_org?: string[];
  arr_managers_lvl_1?: string[];
  arr_managers_lvl_2?: string[];
  arr_managers_lvl_slt?: string[];
  arr_management_chain?: string[];
};

export type TargetDocumentListFilters = {
  str_employee_name_id?: string;
  str_manager_name_id?: string;
  arr_status?: string[]; // Optional
  arr_display?: string[]; // Optional, used for team scope
  str_zone_id?: string; // Optional
  arr_slt_level?: string[]; // Optional
  str_employee_position?: string; // Optional
  str_function?: string; // Optional
  str_function_abi_entity?: string; // Optional
  str_function_abi_entity_2?: string; // Optional
  str_org_name_id?: string; // Optional
  int_year?: number; // Optional
  arr_years?: number[]; // Optional
  arr_macroentity?: string[]; // Optional
  arr_supervisory_org?: string[]; // Optional
  arr_management_chain?: string[]; // Optional, used in combination for supervisory org
  arr_uid_bonus_schema?: string[]; // Optional
  arr_managers_lvl_1?: string[]; // Optional
  arr_managers_lvl_2?: string[]; // Optional
  arr_managers_lvl_slt?: string[]; // Optional
  int_month?: number;
  full_scope?: boolean;
};

export type TargetDocumentCount = {
  [label in (typeof STATUS_NAME)[keyof typeof STATUS_NAME]]?: number;
} & {
  total: number;
};
