import { KeyboardEvent } from 'react';
import { Avatar, Box, Flex, Text, Tooltip } from '@ghq-abi/design-system';
import { ArrowRightIcon, CalendarIcon } from '@ghq-abi/design-system-icons';
import styled from 'styled-components';
import { isNull } from 'lodash';
import configs from '../../../services/configs';
import {
  STATUS_CAPTION_ICON,
  STATUS_COLOR_BG,
  STATUS_COLOR_BG_HOVER,
  STATUS_NAME,
} from '../constants';
import formatDate from '../../../utils/FormatDate';
import { PreAssignationTooltip } from './PreAssignationTooltip';
import { TargetDocumentListObjectType } from '../types';
import { SharedTargetIcon } from '../icons';

const StyledWrapper = styled.div<{ status: string; selected: boolean }>`
  display: grid;
  padding: 8px;
  background: ${({ status, selected }) =>
    selected ? '#325A6D' : STATUS_COLOR_BG[STATUS_NAME[status]]};
  border-radius: 4px;
  gap: 8px;
  border: none;
  cursor: pointer;
  &:active,
  &:focus {
    box-shadow: 0 0 0 1px
      ${({ status }) => STATUS_COLOR_BG[STATUS_NAME[status]]};
  }
  &:hover {
    background: ${({ status, selected }) =>
      selected ? '#1F465A' : STATUS_COLOR_BG_HOVER[STATUS_NAME[status]]};
  }
  max-height: 5rem;
  max-width: 25.37rem;

  svg: {
    path: {
      fill: '#000' !important;
    }
  }
`;

export const TargetDocumentListItem = ({
  employee,
  isSelected,
  onClick,
}: {
  employee: TargetDocumentListObjectType;
  isSelected: boolean;
  onClick: () => Promise<void>;
}) => {
  const urlImage =
    configs.photo_api_url && employee.int_employee_global_id
      ? `${configs.photo_api_url}/${employee.int_employee_global_id}`
      : undefined;

  const icon = STATUS_CAPTION_ICON[employee.str_status];
  const isNotStatusApproval = [
    'CASCADING_EMPLOYEE_APPROVAL',
    'CASCADING_APPRAISER_APPROVAL',
    'FEEDBACK_GIVEN',
    'APPRAISAL_EMPLOYEE_APPROVAL',
    'APPRAISAL_APPRAISER_APPROVAL',
  ].includes(employee.str_status);

  const content = (
    <StyledWrapper
      status={employee.str_status}
      selected={isSelected}
      tabIndex={0}
      role="button"
      onClick={onClick}
      onKeyDown={(event: KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') onClick();
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Flex
          css={{
            alignItems: 'center',
            gap: '1rem',
          }}
        >
          <Avatar
            size="sm"
            name={employee.str_employee_name}
            src={urlImage}
            enablePreLoading
            imageCss={{
              backgroundColor: 'transparent',
              backgroundImage: 'none',
            }}
          />
          <Flex css={{ flexDirection: 'column' }}>
            <Box>
              <Text
                css={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: isSelected ? '$white' : '',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  width: '306px',
                  overflow: 'hidden',
                }}
              >
                {employee.str_employee_name}
              </Text>
              <Box
                css={{
                  display: 'flex',
                  gap: '0.50rem',
                  alignItems: 'center',
                }}
              >
                <CalendarIcon
                  color={isSelected ? '#FFFFFF' : '#7D8597'}
                  width={12}
                  height={12}
                />

                <Text
                  css={{
                    fontWeight: '$normal',
                    color: isSelected ? '$white' : '#7D8597',
                    fontSize: '0.62rem',
                  }}
                >
                  {formatDate(employee.dat_start)}
                </Text>
                <ArrowRightIcon
                  width={12}
                  height={12}
                  color={isSelected ? '#FFFF' : '#7D8597'}
                />
                <Text
                  css={{
                    fontWeight: '$normal',
                    color: isSelected ? '$white' : '#7D8597',
                    fontSize: '0.62rem',
                  }}
                >
                  {formatDate(employee.dat_finish)}
                </Text>
              </Box>
            </Box>
          </Flex>
        </Flex>
        <Box
          css={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.2rem',
            svg: {
              path: {
                fill: isSelected && !isNotStatusApproval ? '#FFF' : '',
              },
            },
          }}
        >
          <Box css={{ display: 'flex', alignItems: 'center' }}>
            {!isNull(employee.id_spoc) && <SharedTargetIcon />}
            {icon}
          </Box>
        </Box>
      </div>
    </StyledWrapper>
  );

  if (!isNull(employee.id_spoc)) {
    return (
      <Tooltip
        align="end"
        content={(<PreAssignationTooltip employee={employee} />) as any}
        side="top"
      >
        {content}
      </Tooltip>
    );
  }

  return content;
};
