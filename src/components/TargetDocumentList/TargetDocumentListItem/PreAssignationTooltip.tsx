import { useMemo } from 'react';
import { Avatar, Flex, Text } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { capitalizeFirstLetters } from '../../../utils/capitalizeFirstLetters';
import configs from '../../../services/configs';
import { TargetDocumentListObjectType } from '../types';

export const PreAssignationTooltip = ({
  employee,
}: {
  employee: Pick<
    TargetDocumentListObjectType,
    'id_spoc' | 'global_id_spoc' | 'str_name_spoc' | 'str_function_spoc'
  >;
}) => {
  const { t } = useTranslation();
  const urlImageSpoc = useMemo(() => {
    return configs.photo_api_url && employee.global_id_spoc
      ? `${configs.photo_api_url}/${employee.global_id_spoc}`
      : undefined;
  }, [employee]);

  return (
    <Flex gap="sm" direction="column">
      <Flex gap="xs">
        <Avatar
          size="sm"
          name={employee.str_name_spoc || ''}
          enablePreLoading
          src={urlImageSpoc}
          imageCss={{
            backgroundColor: 'transparent',
            backgroundImage: 'none',
            width: '25px',
            height: '25px',
          }}
        />
        <Text
          css={{
            color: '$white',
            fontSize: '0.75rem',
            fontWeight: '$bold',
          }}
          dangerouslySetInnerHTML={{
            __html: t('message_shared_target_pre_assignation_owner'),
          }}
        />
      </Flex>
      <Flex direction="column">
        <Text
          css={{
            color: '$white',
            fontSize: '0.75rem',
            fontWeight: '$bold',
          }}
        >
          {capitalizeFirstLetters(employee.str_name_spoc || '')}
        </Text>
        <Text
          css={{
            color: '$white',
            fontSize: '0.75rem',
            fontWeight: '$medium',
          }}
        >
          {t('message_shared_target_pre_assignation_area_manager')} -{' '}
          {employee.str_function_spoc}
        </Text>
      </Flex>
    </Flex>
  );
};
