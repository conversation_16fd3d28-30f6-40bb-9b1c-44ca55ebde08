import { useMemo } from 'react';
import { Flex, Text } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { Filters } from './Filters';
import { TargetDocumentListItem } from '../TargetDocumentListItem/TargetDocumentListItem';
import { TargetDocumentFilterDataProvider } from '../hooks/useTargetDocumentFilterData';
import { standardCurrentStatuses } from '../../../utils';
import { TargetDocumentStatusCaption } from '../TargetDocumentStatusCaption/TargetDocumentStatusCaption';
import { STATUS_NAME } from '../constants';
import { useTargetDocumentSelection } from '../../../hooks/useTargetDocumentSelection';
import { useTargetDocumentList } from '../hooks/useTargetDocumentList';
import { InfiniteScrollListV2 } from '../InfiniteScrollList/InfiniteScrollListV2';

export const TargetDocumentListCurrent = () => {
  const { t } = useTranslation();
  const { fetchAndUpdateSelection, selected, setSelected } =
    useTargetDocumentSelection();
  const {
    targetDocuments,
    loading,
    totalRows,
    hasMoreData,
    filters,
    refresh,
    pageIndex,
  } = useTargetDocumentList();

  const statusesCounter = useMemo(
    () =>
      Object.entries(totalRows)
        .filter(([k]) => k !== 'total')
        .reduce((acc, [k, v]) => {
          const statusName = STATUS_NAME[k];
          if (!Number.isInteger(v) || !statusName) return acc;

          if (acc[statusName])
            return { ...acc, [statusName]: acc[statusName] + v };

          return { ...acc, [statusName]: v };
        }, {}),
    [totalRows],
  );

  const updateSelected =
    (infiniteScroll = false) =>
    tds => {
      if (
        !infiniteScroll ||
        !tds.some(td => selected && td.uid === selected.uid)
      )
        setSelected(undefined);
    };

  return (
    <Flex direction="column" gap="md" css={{ height: '665px' }}>
      <TargetDocumentFilterDataProvider
        statuses={standardCurrentStatuses}
        year={filters.int_year}
        all_abi={false}
        key={filters.int_year}
      >
        <Filters
          filters={filters}
          refresh={refresh}
          updateSelected={updateSelected(false)}
        />
      </TargetDocumentFilterDataProvider>
      {!loading && (
        <Flex
          direction="column"
          justify="between"
          align="start"
          css={{ height: '32px' }}
        >
          <Flex gap="sm">
            <Text css={{ fontWeight: '$medium' }}>
              {t('common_employees')}:
            </Text>
            <Text css={{ fontWeight: '$normal', color: '$gray575' }}>
              {totalRows.total}
            </Text>
          </Flex>
          <Flex gap="sm" align="end">
            {Object.entries(statusesCounter).map(([k, v]) => (
              <TargetDocumentStatusCaption
                key={k}
                status={k as (typeof STATUS_NAME)[keyof typeof STATUS_NAME]}
                count={v as number}
              />
            ))}
          </Flex>
        </Flex>
      )}
      <InfiniteScrollListV2
        hasData={!loading && !!targetDocuments.length}
        hasMoreData={hasMoreData}
        loading={loading}
        page={pageIndex}
        onLoadMore={() => {
          if (loading) return;

          refresh(undefined, updateSelected(true), pageIndex + 1, 100);
        }}
      >
        {targetDocuments.map(employee => (
          <TargetDocumentListItem
            employee={employee}
            key={employee.uid}
            isSelected={selected?.uid === employee.uid}
            onClick={() => fetchAndUpdateSelection(employee.uid)}
          />
        ))}
      </InfiniteScrollListV2>
    </Flex>
  );
};
