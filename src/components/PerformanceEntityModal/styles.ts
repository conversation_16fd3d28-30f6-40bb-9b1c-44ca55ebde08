import { Dialog, styled } from '@ghq-abi/design-system';

export const StyledTitle = styled(Dialog.Title, {
  fontSize: '$4',
  fontWeight: '$medium',
  lineHeight: '$6',
  textAlign: 'left',
  color: '#191F2E',
});

export const StyledDescription = styled(Dialog.Description, {
  fontSize: '$3',
  fontWeight: '$normal',
  lineHeight: '$5',
  textAlign: 'left',
  color: '#191F2E',
});

export const StyledSeeButton = styled('button', {
  display: 'flex',
  p: 0,
  alignItems: 'center',
  gap: '$2',
  fontSize: '$1',
  fontStyle: 'italic',
  fontWeight: '$normal',
  color: '#3F465A',
  backgroundColor: 'transparent',
  border: 'none',

  svg: {
    width: '$3-5',
    height: '$3-5',
  },
});
