import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flex, Dialog, Skeleton } from '@ghq-abi/design-system';
import { InfoCircleFillIcon } from '@ghq-abi/design-system-icons';
import { message } from 'antd';
import { StyledDescription, StyledSeeButton, StyledTitle } from './styles';
import { PerformanceEntity } from '../PerformanceEntityTooltip/types';
import { listPerformanceEntityByTargetDocument } from '../../services/targetDocument/performanceEntityService';

type PerformanceEntityModal = {
  uidTargetDocument: string;
};

export function PerformanceEntityModal({
  uidTargetDocument,
}: PerformanceEntityModal) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const [performanceEntities, setPerformanceEntities] =
    useState<Array<PerformanceEntity> | null>(null);

  const getPerformanceEntityByTargetDocument = useCallback(() => {
    setLoading(true);
    listPerformanceEntityByTargetDocument(uidTargetDocument)
      .then(res => {
        if (res.success) {
          setPerformanceEntities(res.data);
        } else {
          message.error(res.errorMessage, 1);
        }
      })
      .finally(() => setLoading(false));
  }, [uidTargetDocument]);

  useEffect(() => {
    getPerformanceEntityByTargetDocument();
  }, [getPerformanceEntityByTargetDocument]);

  return (
    <Dialog>
      <Dialog.Trigger asChild>
        <StyledSeeButton type="button">
          {t('common_see_performance_entity')}
          <InfoCircleFillIcon />
        </StyledSeeButton>
      </Dialog.Trigger>
      <Dialog.Content title="Entity Targets" variant="sheet">
        <Flex direction="column" gap="sm">
          {loading && <Skeleton height={48} width="100%" variant="rectangle" />}
          {(!performanceEntities || !performanceEntities.length) &&
            !loading && (
              <StyledDescription>
                {t('common_no_performance_entity')}
              </StyledDescription>
            )}
          {performanceEntities?.length &&
            performanceEntities.map(item => (
              <>
                <StyledTitle>{item.str_name}</StyledTitle>
                {item.obj_entities?.map(entity => (
                  <StyledDescription key={entity.str_name}>
                    {`${entity.str_name}: ${entity.int_weight}%`}
                  </StyledDescription>
                ))}
              </>
            ))}
        </Flex>
      </Dialog.Content>
    </Dialog>
  );
}
