import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonV2, config, Flex, TextArea } from '@ghq-abi/design-system';
import { SendIcon } from '@ghq-abi/design-system-icons';
import ModalV2 from '../modalV2/ModalV2';

export const RejectModal = ({
  open,
  loading,
  onClose,
  onReject,
}: {
  open: boolean;
  loading: boolean;
  onClose: () => void;
  onReject: (reason: string) => void;
}) => {
  const { t } = useTranslation();
  const [reason, setReason] = useState('');

  return (
    <ModalV2
      open={open}
      title={t('common_reject_reason')}
      hasFooter={false}
      onCancel={onClose}
    >
      <div>
        <div style={{ padding: '20px', background: config.theme.colors.white }}>
          <TextArea
            style={{
              height: '200px',
              width: '100%',
              background: config.theme.colors.gray50,
            }}
            placeholder={t('common_insert_your_comment')}
            minLength={5}
            value={reason}
            onChange={e => setReason(e.target.value)}
          />
        </div>
        <Flex justify="center" gap="sm" style={{ marginTop: '16px' }}>
          <ButtonV2 size="md" variant="secondary" onClick={onClose}>
            {t('common_cancel')}
          </ButtonV2>
          <ButtonV2
            loading={loading}
            size="md"
            leftIcon={<SendIcon />}
            onClick={() => onReject(reason)}
          >
            {t('common_send_feedback')}
          </ButtonV2>
        </Flex>
      </div>
    </ModalV2>
  );
};
