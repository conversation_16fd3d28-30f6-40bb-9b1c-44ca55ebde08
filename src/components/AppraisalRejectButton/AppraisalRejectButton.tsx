import { ButtonV2 } from '@ghq-abi/design-system';
import { useState } from 'react';
import { XCircleFillIcon } from '@ghq-abi/design-system-icons';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { useTargetDocumentBlocker } from '../TargetDocumentBlocker/useTargetDocumentBlocker';
import appraisalService from '../../services/appraisal/appraisalService';
import { RejectModal } from './RejectModal';

export const AppraisalRejectButton = ({
  targetDocument,
  onUpdate,
}: {
  targetDocument: {
    uid: string;
    csv_myr_requests_to_freeze_kpis?: number | string;
    int_mid_year_review_request_id?: number;
  };
  onUpdate: (newData: {
    str_status: string;
    str_status_name: string;
    str_reject_reason: string;
    bol_can_edit: number;
  }) => void;
}) => {
  const { openWarning } = useTargetDocumentBlocker(targetDocument);
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleReject = (reason: string) => {
    setLoading(true);
    appraisalService
      .rejectTargetDocument(targetDocument.uid, reason)
      .then(res => {
        if (res.success) {
          message.success(
            { content: t('message_target_document_rejected') },
            1,
          );
          onUpdate({
            str_status: 'APPRAISAL_EMPLOYEE_IN_PROGRESS',
            str_status_name: t('appraisal_employee_in_progress'),
            str_reject_reason: reason,
            bol_can_edit: 0,
          });
        } else {
          message.error({ content: res.errorMessage }, 1);
        }
      })
      .finally(() => {
        setLoading(false);
        setModalOpen(false);
      });
  };

  return (
    <>
      <ButtonV2
        size="md"
        loading={loading}
        variant="secondary"
        leftIcon={<XCircleFillIcon width={20} height={20} />}
        style={{
          borderColor: '#FF3236',
          color: '#FF3236',
        }}
        onClick={() => {
          if (!openWarning()) setModalOpen(true);
        }}
      >
        {t('common_reject')}
      </ButtonV2>
      <RejectModal
        loading={loading}
        onClose={() => setModalOpen(false)}
        onReject={handleReject}
        open={modalOpen}
      />
    </>
  );
};
