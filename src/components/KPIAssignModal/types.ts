export type EmployeeListData = {
  uid: string;
  int_weight: number;
  str_value: string | number;
  str_pa_value: string;
  str_scope: string;
  selected: boolean;
  dat_start: string;
  dat_finish: string;
  str_employee_name: string;
  str_positiontitle: string;
  int_employeeglobalid: number;
};

export type EmployeeCatalogFilters = {
  str_employee_name_id: string;
  str_employee_position: string;
  int_pageindex: number;
  int_pagesize: number;
  arr_status: string[];
};

export type KPICatalogFiltersType = {
  orderBy?: {
    attribute:
      | 'str_kpi_name'
      | 'str_short_zone_name'
      | 'str_kpi_type'
      | 'str_source';
    order: 'asc' | 'desc';
  };
  searchKpi?: string;
  searchCalculationMethod?: string;
  pageNumber: number;
  pageSize: number;
  years: number[];
  businesFunctions: string[];
  types: string[];
  favorites: boolean;
};

export type SelectedKPIType = {
  uid_type: string;
  uid: string;
  str_value?: string | number;
  str_scope?: string;
  int_weight?: number;
  str_pa_value?: string;
};
