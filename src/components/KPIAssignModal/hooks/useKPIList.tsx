import {
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';
import { IListCatalogReturn } from '../../../services/kpi/types';
import { KPICatalogFiltersType } from '../types';
import kpiService from '../../../services/kpi/kpiService';

const DEFAULT_FETCH_DELAY = 1000;

const INITIAL_STATE: KPICatalogFiltersType = {
  pageNumber: 1,
  pageSize: 10,
  orderBy: {
    attribute: 'str_kpi_name',
    order: 'asc',
  },
  searchKpi: '',
  searchCalculationMethod: '',
  years: [new Date().getFullYear()],
  businesFunctions: [],
  types: [],
  favorites: false,
};

let fetchListTimeout: NodeJS.Timeout;

const KPIListContext = createContext(
  {} as {
    loading: boolean;
    data?: IListCatalogReturn;
    refresh: (args?: Partial<KPICatalogFiltersType>, delay?: number) => void;
    filters: KPICatalogFiltersType;
    clearFilters: () => void;
  },
);

export const KPIListProvider: React.FC<
  PropsWithChildren & { year?: number }
> = ({ children, year }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<IListCatalogReturn>();
  const [filters, setFilters] = useState<KPICatalogFiltersType>({
    ...INITIAL_STATE,
    years: year ? [year] : INITIAL_STATE.years,
  });

  const refresh = (
    args?: Partial<KPICatalogFiltersType>,
    delay = DEFAULT_FETCH_DELAY,
  ) => {
    const newFilters = { ...filters, ...args };
    setFilters(newFilters);
    clearTimeout(fetchListTimeout);
    fetchListTimeout = setTimeout(() => {
      setLoading(true);
      kpiService
        .listCatalog({
          int_pageindex: newFilters.pageNumber,
          int_pagesize: newFilters.pageSize,
          str_kpi_name_or_id: newFilters.searchKpi,
          str_calculation_method: newFilters.searchCalculationMethod
            ? [newFilters.searchCalculationMethod]
            : [],
          arr_business_functions: newFilters.businesFunctions,
          arr_types: newFilters.types,
          arr_years: newFilters.years,
          bol_favorite: newFilters.favorites,
          order_by: newFilters.orderBy?.attribute,
          order_by_direction: newFilters.orderBy?.order,
        })
        .then(setData)
        .finally(() => setLoading(false));
    }, delay);
  };

  useEffect(() => {
    refresh({}, 0);
    return () => clearTimeout(fetchListTimeout);
  }, []);

  return (
    <KPIListContext.Provider
      value={{
        loading,
        data,
        refresh,
        filters,
        clearFilters: () => refresh(INITIAL_STATE, 0),
      }}
    >
      {children}
    </KPIListContext.Provider>
  );
};

export const useKPIList = () => {
  const context = useContext(KPIListContext);

  if (context === undefined) {
    throw new Error('useKPIList must be used within a KPIListProvider');
  }
  return context;
};
