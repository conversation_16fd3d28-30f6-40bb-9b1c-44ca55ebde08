import { useEffect, useState } from 'react';
import { message } from 'antd';
import { IFilterOptions } from '../../../modules/Admin/catalog/listCatalog/interface';
import kpiService from '../../../services/kpi/kpiService';

/**
 * Custom hook that fetches data to populate KPI listing filter values.
 * This hook does not require a provider, as it is only used in the component responsible for building the filters.
 *
 * @returns {Object} An object containing:
 *  - dataFilters {IFilterOptions}: The filter options data.
 *  - loading {boolean}: Indicates whether data is currently being fetched.
 */
export const useKPIFilterData = () => {
  const [dataFilters, setDataFilters] = useState<IFilterOptions>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    kpiService
      .listDropDown()
      .then(res => {
        if (!res.success) {
          message.error({ content: res.errorMessage, key: 'update' }, 10);
        } else {
          setDataFilters(res.data || {});
        }
      })
      .finally(() => setLoading(false));
  }, []);

  return { dataFilters, loading };
};
