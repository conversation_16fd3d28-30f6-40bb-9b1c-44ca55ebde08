import { useTranslation } from 'react-i18next';
import { KPICatalogFavoriteButton } from '../components/KPICatalogFavoriteButton/KPICatalogFavoriteButton';
import { ICustomColumn } from '../../genericTable/interface';

export const useColumns = () => {
  const { t } = useTranslation();

  const columns: ICustomColumn[] = [
    {
      title: t('common_target_name'),
      dataIndex: 'str_kpi_name',
      defaultSortOrder: 'ascend',
      sorter: true,
      width: 150,
      render: val => (
        <div className="kpi-catalog__truncate-1" title={val}>
          {val}
        </div>
      ),
    },
    {
      title: t('kpi_id'),
      dataIndex: 'str_kpi_id',
      width: 100,
    },
    {
      title: t('common_zone'),
      width: 100,
      dataIndex: 'str_short_zone_name',
      sorter: true,
    },
    {
      title: t('kpi_calculation_method'),
      dataIndex: 'str_calculation_method',
      sorter: false,
      render: val => (
        <div className="kpi-catalog__truncate-1" title={val}>
          {val}
        </div>
      ),
    },
    {
      title: t('common_source'),
      dataIndex: 'str_source',
      render: val => (
        <div className="kpi-catalog__truncate-1" title={val}>
          {val}
        </div>
      ),
      width: 220,
      sorter: true,
    },
    {
      title: t('kpi_business_function'),
      dataIndex: 'str_business_function_name',
      width: 145,
      sorter: true,
    },
    {
      title: '',
      dataIndex: '',
      width: 70,
      sorter: false,
      render: (_, record) => <KPICatalogFavoriteButton record={record} />,
    },
  ];
  return columns;
};
