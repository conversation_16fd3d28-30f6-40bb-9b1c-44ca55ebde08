import {
  Dispatch,
  PropsWithChildren,
  SetStateAction,
  createContext,
  useContext,
  useMemo,
  useState,
} from 'react';
import { SelectedKPIType } from '../types';

type BatchFieldsType = {
  weight?: number;
  value?: string | number;
  paValue?: string;
  scope?: string;
};

const BatchFieldsContext = createContext(
  {} as {
    batchFields: BatchFieldsType;
    setBatchFields: Dispatch<SetStateAction<BatchFieldsType>>;
  },
);

export const INITIAL_STATE = {
  weight: 0,
  value: '',
  paValue: '',
  scope: '',
};

export const BatchFieldsProvider: React.FC<
  PropsWithChildren & {
    customInitialState?: SelectedKPIType;
  }
> = ({ children, customInitialState }) => {
  const [batchFields, setBatchFields] = useState<BatchFieldsType>(
    customInitialState
      ? {
          weight: customInitialState?.int_weight,
          scope: customInitialState?.str_scope,
          paValue: customInitialState?.str_pa_value,
          value: customInitialState?.str_value,
        }
      : INITIAL_STATE,
  );

  const providerValues = useMemo(
    () => ({
      batchFields,
      setBatchFields,
    }),
    [batchFields, setBatchFields],
  );

  return (
    <BatchFieldsContext.Provider value={providerValues}>
      {children}
    </BatchFieldsContext.Provider>
  );
};

export const useBatchFields = () => {
  const context = useContext(BatchFieldsContext);

  if (context === undefined) {
    throw new Error('useBatchFields must be used within a BatchFieldsContext');
  }
  return context;
};
