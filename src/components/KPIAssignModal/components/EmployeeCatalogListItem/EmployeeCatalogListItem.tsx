import { Checkbox, Input, Select, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import TextArea from 'antd/lib/input/TextArea';
import { StyledLabel } from '../../styles';
import { EmployeeListData, SelectedKPIType } from '../../types';
import targetDocumentService from '../../../../services/targetDocument/targetDocumentService';
import { ValueField } from '../../../KPIValueField/ValueField';
import { EmployeeCatalogDetailsColumn } from '../EmployeeCatalogDetailsColumn/EmployeeCatalogDetailsColumn';
import { KpiTypeUuids } from '../../../../utils';
import { EmployeeListItemWrapper } from './styles';
import { getPaValueByValueAndKpiType } from '../../../../businessRules';
import { DISABLED_PA_VALUES } from '../../../../constants';

export const EmployeeCatalogListItem = ({
  line,
  onUpdate,
  selectedKPI,
}: {
  line: EmployeeListData;
  onUpdate: (uid: string, newData: Partial<EmployeeListData>) => void;
  selectedKPI: SelectedKPIType;
}) => {
  const { t } = useTranslation();
  return (
    <EmployeeListItemWrapper key={line.uid}>
      <Checkbox
        checked={line.selected}
        onChange={e => {
          onUpdate(line.uid, {
            selected: e.target.checked,
          });
        }}
      />
      <EmployeeCatalogDetailsColumn line={line} />

      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 2fr 2fr',
          gap: '10px',
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_weight')}</StyledLabel>
          <Select
            style={{ width: '100%' }}
            showSearch
            size="large"
            listHeight={100}
            placeholder={t('common_weight')}
            options={targetDocumentService.catalogWeightList()}
            getPopupContainer={trigger => trigger.parentNode}
            value={line.int_weight}
            onChange={value => onUpdate(line.uid, { int_weight: value })}
          />
        </Space>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_value')}</StyledLabel>
          <ValueField
            selectedKPI={selectedKPI}
            value={line.str_value}
            onChange={value => {
              const paValue = getPaValueByValueAndKpiType(
                value ? value.toString() : '',
                selectedKPI.uid_type as KpiTypeUuids,
                line.str_pa_value,
              );
              onUpdate(line.uid, { str_value: value, str_pa_value: paValue });
            }}
          />
        </Space>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_pa_value')}</StyledLabel>
          <Input
            value={line.str_pa_value}
            size="large"
            disabled={DISABLED_PA_VALUES.includes(selectedKPI.uid_type)}
            name="str_pa_value"
            placeholder="N/A"
            onChange={e =>
              onUpdate(line.uid, {
                str_pa_value: e.target.value,
              })
            }
          />
        </Space>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_scope')}</StyledLabel>
          <TextArea
            value={line.str_scope}
            minLength={1}
            size="middle"
            style={{ height: '40px' }}
            name="str_scope"
            onChange={e =>
              onUpdate(line.uid, {
                str_scope: e.target.value,
              })
            }
          />
        </Space>
      </div>
    </EmployeeListItemWrapper>
  );
};
