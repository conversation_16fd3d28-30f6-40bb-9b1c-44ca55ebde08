import { Col, Input, Row, Select, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import TextArea from 'antd/lib/input/TextArea';
import targetDocumentService from '../../../../services/targetDocument/targetDocumentService';
import { ValueField } from '../../../KPIValueField/ValueField';
import { StyledLabel } from '../../styles';
import { KpiTypeUuids } from '../../../../utils';
import { useBatchFields } from '../../hooks/useBatchFields';
import { SelectedKPIType } from '../../types';
import { DISABLED_PA_VALUES } from '../../../../constants';
import { getPaValueByValueAndKpiType } from '../../../../businessRules';

export const EmployeeCatalogBatchFields = ({
  selectedKPI,
  onChange,
}: {
  selectedKPI: SelectedKPIType;
  onChange: (args: {
    int_weight?: number;
    str_value?: string | number;
    str_pa_value?: string;
    str_scope?: string;
  }) => void;
}) => {
  const { t } = useTranslation();
  const { batchFields, setBatchFields } = useBatchFields();

  return (
    <Row
      gutter={10}
      style={{ width: '100%', background: '#FBFAFC', padding: '16px' }}
    >
      <Col span={4}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_weight')}</StyledLabel>
          <Select
            style={{ width: '100%' }}
            showSearch
            size="large"
            listHeight={100}
            placeholder={t('common_weight')}
            options={targetDocumentService.catalogWeightList()}
            getPopupContainer={trigger => trigger.parentNode}
            value={batchFields.weight}
            onChange={value => {
              const newValue = {
                ...batchFields,
                int_weight: value,
                weight: value,
              };
              setBatchFields(newValue);
              onChange(newValue);
            }}
          />
        </Space>
      </Col>
      <Col span={4}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_value')}</StyledLabel>
          <ValueField
            selectedKPI={selectedKPI}
            value={batchFields.value}
            onChange={value => {
              const paValueNew = getPaValueByValueAndKpiType(
                value ? value.toString() : '',
                selectedKPI.uid_type as KpiTypeUuids,
                batchFields.paValue,
              );
              const newValue = {
                ...batchFields,
                str_value: value,
                str_pa_value: paValueNew,
                value,
              };
              setBatchFields(newValue);
              onChange(newValue);
            }}
          />
        </Space>
      </Col>
      <Col span={4}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_pa_value')}</StyledLabel>
          <Input
            value={batchFields.paValue}
            size="large"
            placeholder="N/A"
            disabled={DISABLED_PA_VALUES.includes(selectedKPI.uid_type)}
            name="str_pa_value"
            title={batchFields.paValue}
            onChange={e => {
              const newValue = {
                ...batchFields,
                str_pa_value: e.target.value,
                paValue: e.target.value,
              };
              setBatchFields(newValue);
              onChange(newValue);
            }}
          />
        </Space>
      </Col>
      <Col span={12}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <StyledLabel>{t('common_scope')}</StyledLabel>
          <TextArea
            value={batchFields.scope}
            minLength={1}
            size="middle"
            style={{ height: '40px' }}
            name="str_scope"
            onChange={e => {
              const newValue = {
                ...batchFields,
                str_scope: e.target.value,
                scope: e.target.value,
              };
              setBatchFields(newValue);
              onChange(newValue);
            }}
          />
        </Space>
      </Col>
    </Row>
  );
};
