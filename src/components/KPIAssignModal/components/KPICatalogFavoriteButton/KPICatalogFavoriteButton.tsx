import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { ButtonV2 } from '@ghq-abi/design-system';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import kpiService from '../../../../services/kpi/kpiService';

export const KPICatalogFavoriteButton = ({ record }: { record: any }) => {
  const { t } = useTranslation();
  const [favorite, setFavorite] = useState(record.bol_favorite);
  const [loading, setLoading] = useState(false);
  return (
    <ButtonV2
      loading={loading}
      variant="borderless"
      size="sm"
      leftIcon={
        <FontAwesomeIcon
          style={{ width: '14px', height: '14px' }}
          icon={favorite ? 'star' : ['far', 'star']}
        />
      }
      style={{ gap: '0px' }}
      title={t('common_add_favorites')}
      onClick={async () => {
        setLoading(true);
        try {
          const addOrRemove = favorite
            ? kpiService.removeFromMyCart
            : kpiService.addToMyCart;
          const response = await addOrRemove({ uid: record.uid });

          if (!response.success) throw new Error(response.errorMessage);

          setFavorite(!favorite);
        } catch (error) {
          message.error({ content: (error as Error).message }, 1);
        } finally {
          setLoading(false);
        }
      }}
    />
  );
};
