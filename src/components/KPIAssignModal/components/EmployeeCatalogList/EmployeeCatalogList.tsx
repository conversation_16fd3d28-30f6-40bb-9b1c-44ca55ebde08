import InfiniteScroll from 'react-infinite-scroller';
import { List, Row, Spin } from 'antd';
import { EmployeeListInfiniteContainer } from './styles';
import { EmployeeCatalogListItem } from '../EmployeeCatalogListItem/EmployeeCatalogListItem';
import { EmployeeListData, SelectedKPIType } from '../../types';

export const EmployeeList = ({
  selectedKPI,
  data,
  loading,
  loadingMore,
  loadMore,
  hasMoreData,
  onUpdate,
}: {
  selectedKPI: SelectedKPIType;
  loading: boolean;
  loadingMore: boolean;
  data: EmployeeListData[];
  hasMoreData: boolean;
  loadMore: () => void;
  onUpdate: (uid: string, newData: Partial<EmployeeListData>) => void;
}) => {
  return (
    <div style={{ position: 'relative' }}>
      <EmployeeListInfiniteContainer>
        <InfiniteScroll
          initialLoad={false}
          loadMore={loadMore}
          hasMore={!loading && hasMoreData}
          useWindow={false}
          threshold={100}
          style={{
            display: 'grid',
            gap: '10px',
            padding: '10px 10px 10px 0px',
          }}
        >
          <List
            loading={loading}
            grid={{
              gutter: 16,
            }}
            dataSource={data}
            renderItem={line => (
              <EmployeeCatalogListItem
                line={line}
                onUpdate={onUpdate}
                selectedKPI={selectedKPI}
                key={line.uid}
              />
            )}
          />
          {loadingMore && hasMoreData && (
            <Row justify="center" align="middle">
              <Spin />
            </Row>
          )}
        </InfiniteScroll>
      </EmployeeListInfiniteContainer>
    </div>
  );
};
