import { useTranslation } from 'react-i18next';
import { ButtonV2 } from '@ghq-abi/design-system';
import { useState } from 'react';
import { StyledEmployeeFiltersWrapper } from './styles';
import { MyTeamCascadingFilters } from '../../../../services/cascading/types';
import Search from '../../../search/Search';

type EmployeeFiltersProps = {
  filters: MyTeamCascadingFilters;
  refresh: (args: Partial<MyTeamCascadingFilters>) => void;
  prefieldActive: boolean;
  onPrefieldClick: () => void;
};

export const EmployeeCatalogFilters = ({
  filters,
  refresh,
  prefieldActive,
  onPrefieldClick,
}: EmployeeFiltersProps) => {
  const { t } = useTranslation();
  const [employeeNameOrId, setEmployeeNameOrId] = useState(
    filters.str_employee_name_id,
  );
  const [position, setPosition] = useState(filters.str_employee_position);

  return (
    <div>
      <StyledEmployeeFiltersWrapper>
        <Search
          label={t('common_employee')}
          value={employeeNameOrId}
          allowClear
          placeholder={t('common_search_by_name_id')}
          onPressEnter={() => refresh({ int_pageindex: 1 })}
          onChange={e => {
            setEmployeeNameOrId(e.target.value);
            refresh({ str_employee_name_id: e.target.value, int_pageindex: 1 });
          }}
        />
        <Search
          label={t('common_position')}
          value={position}
          allowClear
          placeholder={t('common_search_employee_position')}
          onPressEnter={() => refresh({ int_pageindex: 1 })}
          onChange={e => {
            setPosition(e.target.value);
            refresh({
              str_employee_position: e.target.value,
              int_pageindex: 1,
            });
          }}
        />
        <ButtonV2
          onClick={onPrefieldClick}
          variant={prefieldActive ? 'primary' : 'secondary'}
          size="md"
          style={{ whiteSpace: 'nowrap' }}
        >
          {t('common_prefield')}
        </ButtonV2>
      </StyledEmployeeFiltersWrapper>
    </div>
  );
};
