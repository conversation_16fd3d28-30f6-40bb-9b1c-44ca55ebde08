import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ButtonV2 } from '@ghq-abi/design-system';
import { XIcon } from '@ghq-abi/design-system-icons';
import {
  HiddenFilters,
  HiddenFiltersButton,
  HiddenFiltersProvider,
} from '../../../hiddenFilters';
import Search from '../../../search/Search';
import Select from '../../../select/Select';
import { StyledKPIHiddenFiltersWrapper, StyledKPIMainFilters } from './styles';
import { useKPIList } from '../../hooks/useKPIList';
import { useKPIFilterData } from '../../hooks/useKPIFilterData';

export const KPICatalogFilters = () => {
  const { t } = useTranslation();
  const { filters, loading, refresh, clearFilters } = useKPIList();
  const { dataFilters, loading: loadingFilters } = useKPIFilterData();

  return (
    <HiddenFiltersProvider>
      <StyledKPIMainFilters>
        <div
          style={{
            width: '100%',
            display: 'grid',
            gridTemplateColumns: 'auto auto',
            gap: '10px',
          }}
        >
          <Search
            name="kpi_text"
            label={t('common_kpi')}
            allowClear
            value={filters.searchKpi}
            placeholder={t('entities_filter_name')}
            onPressEnter={() => refresh({ pageNumber: 1 })}
            onChange={e =>
              refresh({ searchKpi: e.target.value, pageNumber: 1 })
            }
          />
          <Search
            name="calculation_method"
            value={filters.searchCalculationMethod}
            allowClear
            label={t('kpi_calculation_method')}
            placeholder={t('kpi_calculation_method')}
            onPressEnter={() => refresh({ pageNumber: 1 })}
            onChange={e =>
              refresh({
                searchCalculationMethod: e.target.value,
                pageNumber: 1,
              })
            }
          />
        </div>

        <HiddenFiltersButton />
        <ButtonV2
          leftIcon={
            <FontAwesomeIcon
              icon={filters.favorites ? 'star' : ['far', 'star']}
            />
          }
          color="blue"
          size="md"
          title={t('common_favorites')}
          style={{ marginTop: 'auto' }}
          onClick={() =>
            refresh({ favorites: !filters.favorites, pageNumber: 1 }, 0)
          }
        >
          {t('common_favorites')}
        </ButtonV2>
      </StyledKPIMainFilters>
      <HiddenFilters greyBackground align="bottom">
        <StyledKPIHiddenFiltersWrapper>
          <div
            style={{
              width: '100%',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '10px',
              alignItems: 'end',
            }}
          >
            <Select
              disabled={loading}
              loading={loadingFilters}
              allowClear
              value={filters.businesFunctions}
              mode="multiple"
              maxTagCount={3}
              placeholder={t('common_select_option')}
              label={t('common_function_abi_entity')}
              options={dataFilters?.json_kpi_business_function?.map(item => ({
                value: item.uid,
                label: item.str_name,
              }))}
              getPopupContainer={trigger => trigger.parentNode}
              onChange={value =>
                refresh({ businesFunctions: value, pageNumber: 1 })
              }
            />
            <Select
              disabled={loading}
              loading={loadingFilters}
              allowClear
              value={filters.types}
              mode="multiple"
              maxTagCount={3}
              placeholder={t('common_select_option')}
              label={t('common_type')}
              options={dataFilters?.json_kpi_types?.map(item => ({
                value: item.uid,
                label: item.str_name,
              }))}
              getPopupContainer={trigger => trigger.parentNode}
              onChange={value => refresh({ types: value, pageNumber: 1 })}
            />
          </div>
          <ButtonV2
            size="md"
            variant="secondary"
            onClick={clearFilters}
            leftIcon={<XIcon style={{ width: '20px', height: '20px' }} />}
            style={{ whiteSpace: 'nowrap' }}
          >
            {t('common_clear_filters')}
          </ButtonV2>
        </StyledKPIHiddenFiltersWrapper>
      </HiddenFilters>
    </HiddenFiltersProvider>
  );
};
