import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Checkbox, message } from 'antd';
import { ButtonV2 } from '@ghq-abi/design-system';
import { EmployeeCatalogFilters } from '../EmployeeCatalogFilters/EmployeeCatalogFilters';
import { EmployeeList } from '../EmployeeCatalogList/EmployeeCatalogList';
import { standardKpiAssignationStatuses } from '../../../../utils';
import { EmployeeCatalogBatchFields } from '../EmployeeCatalogBatchFields/EmployeeCatalogBatchFields';
import { EmployeeCatalogWrapper } from './styles';
import {
  IDataKpi,
  MyTeamCascadingFilters,
} from '../../../../services/cascading/types';
import { TableFloatingSelection } from '../../../TableFloatingSelection/TableFloatingSelection';
import { EmployeeCatalogSaveButton } from '../EmployeeCatalogSaveButton/EmployeeCatalogSaveButton';
import { INITIAL_STATE, useBatchFields } from '../../hooks/useBatchFields';
import { EmployeeListData } from '../../types';
import targetDocumentService from '../../../../services/targetDocument/targetDocumentService';

const INITIAL_LIST_SIZE = 30;

let fetchTimeout: NodeJS.Timeout;

export const EmployeeCatalog = ({
  selectedKPI,
  onSuccess,
  onBack,
  year,
  full_scope = false,
}: {
  selectedKPI: {
    uid_type: string;
    uid: string;
    str_value?: string | number;
    str_scope?: string;
    int_weight?: number;
    str_pa_value?: string;
  };
  onSuccess: (data?: IDataKpi[]) => void;
  onBack?: () => void;
  year?: number | string;
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();
  const { setBatchFields, batchFields } = useBatchFields();
  const [filters, setFilters] = useState<MyTeamCascadingFilters>({
    str_employee_name_id: '',
    str_employee_position: '',
    int_pageindex: 1,
    int_pagesize: INITIAL_LIST_SIZE,
    arr_status: standardKpiAssignationStatuses,
    int_year: +(year ?? new Date().getFullYear()),
  });
  const [loadingMore, setLoadingMore] = useState(false);
  const [initLoading, setInitLoading] = useState(true);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalRows, setTotalRows] = useState(0);
  const [prefieldVisible, setPrefieldVisible] = useState(false);
  const [listData, setListData] = useState<Array<EmployeeListData>>([]);
  const isAllSelected = listData.every(line => line.selected);
  const selecteds = listData.filter(line => line.selected);

  const fetchData = useCallback(
    async (obj: Partial<typeof filters>, appendData = false) => {
      try {
        const newFilters = {
          ...filters,
          ...obj,
          full_scope,
        };
        setFilters(newFilters);
        const [targetDocuments, count] = await Promise.all([
          targetDocumentService.getTargetDocumentsListDetailed(newFilters),
          newFilters.int_pageindex === 1
            ? targetDocumentService.getTargetDocumentsListCount(newFilters)
            : { data: { total: { total: totalRows } } },
        ]);
        const responseData = targetDocuments.data?.lines || [];
        const newListData = responseData.map(line => {
          const currentValue = listData.find(l => l.uid === line.uid);
          return {
            ...line,
            int_weight: currentValue?.int_weight || 0,
            str_value: currentValue?.str_value || 0,
            str_pa_value: currentValue?.str_pa_value || '',
            str_scope: currentValue?.str_scope || '',
            selected: currentValue?.selected || false,
          };
        });
        const data = appendData ? listData.concat(newListData) : newListData;
        setListData(data);
        setTotalRows(count.data.total.total);
        setHasMoreData(count.data.total.total > data.length);
      } catch (err) {
        message.error({
          content: (err as Error).message ?? t('request_error'),
          duration: 15,
        });
        // eslint-disable-next-line no-console
        console.error(err);
      } finally {
        setInitLoading(false);
        setLoadingMore(false);
      }
    },
    [],
  );

  const loadMore = () => {
    setLoadingMore(true);
    fetchData({ int_pageindex: (filters?.int_pageindex || 1) + 1 }, true);
  };

  useEffect(() => {
    fetchData({});
  }, []);

  useEffect(() => {
    setListData(prev =>
      prev.map(p =>
        p.selected
          ? ({
              ...p,
              int_weight: batchFields?.weight,
              str_scope: batchFields?.scope,
              str_pa_value: batchFields?.paValue,
              str_value: batchFields?.value,
            } as unknown as EmployeeListData)
          : p,
      ),
    );
  }, [batchFields, isAllSelected, selecteds]);

  return (
    <EmployeeCatalogWrapper>
      <EmployeeCatalogFilters
        prefieldActive={prefieldVisible}
        onPrefieldClick={() => setPrefieldVisible(!prefieldVisible)}
        filters={filters}
        refresh={obj => {
          setInitLoading(true);
          setHasMoreData(true);
          clearTimeout(fetchTimeout);
          fetchTimeout = setTimeout(() => {
            fetchData(obj);
          }, 1000);
        }}
      />

      {prefieldVisible && (
        <EmployeeCatalogBatchFields
          selectedKPI={selectedKPI}
          onChange={values => {
            setListData(
              listData.map(line => {
                return {
                  ...line,
                  ...(line.selected ? values : {}),
                };
              }),
            );
          }}
        />
      )}
      <Checkbox
        style={{ marginTop: '10px', width: 'fit-content' }}
        checked={isAllSelected}
        onChange={e =>
          setListData(
            listData.map(line => ({
              ...line,
              ...(e.target.checked && prefieldVisible ? batchFields : {}),
              selected: e.target.checked,
            })),
          )
        }
      >
        {t('common_select_all')}
      </Checkbox>
      <EmployeeList
        selectedKPI={selectedKPI}
        onUpdate={(uid, data) => {
          const newListData = listData.map(line => {
            if (line.uid === uid)
              return {
                ...line,
                ...data,
              };

            return line;
          });

          setListData(newListData);
        }}
        hasMoreData={hasMoreData}
        loadMore={loadMore}
        data={listData}
        loading={initLoading}
        loadingMore={loadingMore}
      />

      <TableFloatingSelection
        actionButtons={[
          onBack ? (
            <ButtonV2
              variant="secondary"
              size="sm"
              onClick={() => {
                onBack();
                setBatchFields(INITIAL_STATE);
              }}
              key="back"
            >
              {t('common_back')}
            </ButtonV2>
          ) : null,
          <EmployeeCatalogSaveButton
            key="save"
            onSuccess={data => {
              onSuccess(data);
              setBatchFields(INITIAL_STATE);
            }}
            selectedKPI={selectedKPI}
            selectedRows={selecteds}
          />,
        ]}
        selectedsSize={selecteds.length}
        selectedsText={
          selecteds.length > 1
            ? t('common_selected_employee_plural')
            : t('common_selected_employee')
        }
      />
    </EmployeeCatalogWrapper>
  );
};
