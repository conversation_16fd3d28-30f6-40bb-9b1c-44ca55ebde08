import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { ButtonV2 } from '@ghq-abi/design-system';
import { EmployeeListData, SelectedKPIType } from '../../types';
import cascadingService from '../../../../services/cascading/cascadingService';
import { IDataKpi } from '../../../../modules/MyTeam/Cascading/interface';

export const EmployeeCatalogSaveButton = ({
  selectedKPI,
  selectedRows,
  onSuccess,
}: {
  selectedKPI: SelectedKPIType;
  selectedRows: EmployeeListData[];
  onSuccess: (result: IDataKpi[]) => void;
}) => {
  const { t } = useTranslation();
  const [loadingSave, setLoadingSave] = useState(false);

  const onSave = () => {
    const data = {
      kpi: selectedKPI.uid,
      list: selectedRows.map(item => {
        return {
          uid_target_document: item.uid,
          weight: item.int_weight || null,
          scope: item.str_scope || null,
          value: `${item.str_value}` || null,
          pa_value: item.str_pa_value || null,
        };
      }),
    };
    setLoadingSave(true);
    cascadingService
      .addKpiTargetDocument(data)
      .then(res => {
        if (!res.success) {
          message.error(res.errorMessage);
        } else {
          onSuccess(res.data?.results || []);
        }
      })
      .finally(() => setLoadingSave(false));
  };

  return (
    <ButtonV2
      disabled={!selectedRows.length}
      loading={loadingSave}
      size="sm"
      onClick={onSave}
    >
      {t('common_continue')}
    </ButtonV2>
  );
};
