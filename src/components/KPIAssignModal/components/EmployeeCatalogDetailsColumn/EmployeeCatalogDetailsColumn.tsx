import { Flex, config } from '@ghq-abi/design-system';
import { CalendarIcon } from '@ghq-abi/design-system-icons';
import { EmployeeListData } from '../../types';
import formatDate from '../../../../utils/FormatDate';

export const EmployeeCatalogDetailsColumn = ({
  line,
}: {
  line: EmployeeListData;
}) => {
  return (
    <Flex direction="column" style={{ minWidth: '270px', gap: '5px' }}>
      <span
        style={{
          fontSize: config.theme.sizes['3-5'],
          fontWeight: config.theme.fontWeights.medium,
          color: config.theme.colors.gray750,
        }}
      >
        {line.str_employee_name}
      </span>
      <span
        style={{
          fontSize: '12px',
          fontWeight: 400,
          color: '#7D8597',
        }}
      >
        {line.str_positiontitle}
      </span>
      <div
        style={{
          fontSize: '10px',
          fontWeight: 400,
          color: '#7D8597',
          display: 'flex',
          gap: '10px',
        }}
      >
        <span>{line.int_employeeglobalid}</span>
        <div style={{ borderLeft: '1px solid #7D8597' }} />
        <div style={{ display: 'flex', gap: '5px' }}>
          <CalendarIcon width={12} height={12} />
          <span>{formatDate(line.dat_start)}</span>
          <span>{formatDate(line.dat_finish)}</span>
        </div>
      </div>
    </Flex>
  );
};
