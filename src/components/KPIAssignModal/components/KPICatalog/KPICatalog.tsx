import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonV2 } from '@ghq-abi/design-system';

import { useColumns } from '../../hooks/useColumns';
import { IListCatalogKPI } from '../../../../services/kpi/types';
import { KPICatalogFilters } from '../KPICatalogFilters/KPICatalogFilters';

import { TableFloatingSelection } from '../../../TableFloatingSelection/TableFloatingSelection';
import Select from '../../../select/Select';
import { StyledKPICatalogWrapper, StyledTable } from './styles';
import { useKPIList } from '../../hooks/useKPIList';

const SELECT_OPTS = [10, 20, 30];

const KPICatalog = ({
  onSelect,
  selectMultiple,
  selecteds,
  onNext,
}: {
  onSelect: (selectedKpis: IListCatalogKPI[]) => void;
  selectMultiple?: boolean;
  selecteds: IListCatalogKPI[];
  onNext: () => void;
}) => {
  const { t } = useTranslation();
  const { filters, refresh, loading, data } = useKPIList();
  const columns = useColumns();

  return (
    <StyledKPICatalogWrapper>
      <div className="kpi-catalog__search-container">
        <KPICatalogFilters />
      </div>

      <StyledTable
        hasSelecteds={!!selecteds.length}
        scroll={{ y: 350 }}
        sortDirections={['ascend', 'descend', 'ascend']}
        rowSelection={{
          selectedRowKeys: (selecteds.map(kpi => kpi.uid) as any) || [],
          type: selectMultiple ? 'checkbox' : 'radio',
          onChange: (_: React.Key[], selectedRows: any[]) =>
            onSelect(selectedRows),
        }}
        loading={loading}
        columns={columns}
        dataSource={data?.data?.lines as unknown[]}
        rowKey="uid"
        pagination={{
          position: ['bottomCenter'],
          defaultCurrent: 1,
          total: data?.data?.totalRecords,
          showTotal: () =>
            t('message_mid_year_review_requests_kpis_list_total', {
              total: data?.data?.totalRecords,
            }),
          current: filters?.pageNumber,
          pageSize: filters?.pageSize,
          showSizeChanger: false,
        }}
        onChange={(pagination, _, sorter: any) => {
          const column = sorter?.column?.dataIndex;
          refresh(
            {
              orderBy: column && {
                attribute: column,
                order: sorter.order === 'ascend' ? 'asc' : 'desc',
              },
              pageNumber: pagination.current || 1,
              pageSize: pagination.pageSize,
            },
            0,
          );
        }}
      />
      {data?.data?.totalRecords ? (
        <div className="kpi-catalog__pagination-selector">
          <Select
            value={filters.pageSize}
            options={SELECT_OPTS.map(opt => ({
              label: t('message_mid_year_review_requests_kpis_list_page_size', {
                page_size: opt,
                total: data?.data?.totalRecords,
              }),
              value: opt,
            }))}
            onChange={value => refresh({ pageSize: value, pageNumber: 1 }, 0)}
          />
        </div>
      ) : null}
      {!!selecteds.length && (
        <TableFloatingSelection
          absoluteBottom
          actionButtons={
            <ButtonV2 disabled={!selecteds.length} size="sm" onClick={onNext}>
              {t('common_continue')}
            </ButtonV2>
          }
          clearSelecteds={() => onSelect([])}
          selectedsSize={selecteds.length}
          selectedsText={
            selecteds.length > 1
              ? t('common_selected_kpi_plural')
              : t('common_selected_kpi')
          }
        />
      )}
    </StyledKPICatalogWrapper>
  );
};

export default memo(KPICatalog);
