import styled from 'styled-components';
import { config } from '@ghq-abi/design-system';
import GenericTable from '../../../genericTable/GenericTable';

export const StyledTable = styled(GenericTable)<{ hasSelecteds: boolean }>`
  .ant-pagination {
    margin-top: ${props => (props.hasSelecteds ? '90px' : '0px')} !important;
  }
`;

export const StyledKPICatalogWrapper = styled.div`
  position: relative;
  .ant-table-column-title {
    white-space: nowrap;
    min-width: 70px;
  }
  .kpi-catalog__truncate-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }
  .ant-table-header th {
    font-size: ${config.theme.fontSizes[1]};
    font-weight: ${config.theme.fontWeights.medium};
    color: ${config.theme.colors.gray750};
    white-space: nowrap;
  }
  .ant-pagination {
    background-color: #f5f5f5;
    padding: 12px 20px !important;
    margin: 0px;
    .ant-pagination-total-text {
      position: absolute;
      color: #333333;
      left: 20px;
    }
    .ant-pagination-item-link,
    .ant-pagination-item {
      background: transparent;
      border: none;
      font-weight: ${config.theme.fontWeights.medium};
      color: #000000de;
    }
    .ant-pagination-item-active {
      background: #0000001f;
      font-weight: ${config.theme.fontWeights.bold};
      a {
        color: #000000de;
      }
    }
  }

  .kpi-catalog__pagination-selector {
    width: 160px;
    bottom: 18px;
    right: 0;
    bottom: 8px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: end;
    .ant-select-selection-item {
      font-weight: ${config.theme.fontWeights.bold};
    }
    .ant-select {
      width: fit-content !important;
      font-size: ${config.theme.fontSizes[2]};
    }
    .ant-select-selector {
      background-color: transparent !important;
      border: none !important;
    }
  }
`;
