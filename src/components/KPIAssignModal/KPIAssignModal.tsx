import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { EmployeeCatalog } from './components/EmployeeCatalog/EmployeeCatalog';
import KPICatalog from './components/KPICatalog/KPICatalog';
import { BatchFieldsProvider } from './hooks/useBatchFields';
import { SelectedKPIType } from './types';
import { IListCatalogKPI } from '../../services/kpi/types';
import { IDataKpi } from '../../services/cascading/types';
import ModalV2 from '../modalV2/ModalV2';
import { KPIListProvider } from './hooks/useKPIList';

export const KPIAssignModal = ({
  open,
  selectMultiple,
  onClose,
  onSuccess,
  onSelectMultiple,
  kpiToAssign,
  kpisYear,
  full_scope,
}: {
  open: boolean;
  selectMultiple?: boolean;
  onClose: () => void;
  onSuccess?: (result?: IDataKpi[]) => void;
  onSelectMultiple?: (selected: IListCatalogKPI[]) => void;
  kpiToAssign?: SelectedKPIType;
  kpisYear?: number | string;
  full_scope?: boolean;
}) => {
  const { t } = useTranslation();
  const [selectedsKPIs, setSelectedsKPIs] = useState<IListCatalogKPI[]>([]);
  const [currentStep, setCurrentStep] = useState(1);

  const getTitle = () => {
    if (!selectMultiple) {
      return currentStep === 1 && !kpiToAssign
        ? t('modal_kpi_assign_title')
        : t('modal_employee_assign_title');
    }
    return t('common_select_kpis');
  };

  return (
    <ModalV2
      open={open}
      width="75%"
      title={getTitle()}
      hasFooter={false}
      onCancel={() => {
        onClose();
        setSelectedsKPIs([]);
        setCurrentStep(1);
      }}
    >
      <BatchFieldsProvider customInitialState={kpiToAssign}>
        {kpiToAssign ? (
          <EmployeeCatalog
            selectedKPI={kpiToAssign}
            year={kpisYear}
            full_scope={full_scope}
            onSuccess={data => {
              onClose();
              onSuccess?.(data);
              setSelectedsKPIs([]);
              setCurrentStep(1);
            }}
          />
        ) : currentStep === 2 && selectedsKPIs?.length ? (
          <EmployeeCatalog
            selectedKPI={selectedsKPIs[0]}
            onBack={() => setCurrentStep(1)}
            year={kpisYear}
            full_scope={full_scope}
            onSuccess={() => {
              onClose();
              onSuccess?.();
              setSelectedsKPIs([]);
              setCurrentStep(1);
            }}
          />
        ) : (
          <KPIListProvider year={kpisYear ? +kpisYear : undefined}>
            <KPICatalog
              selecteds={selectedsKPIs}
              selectMultiple={selectMultiple}
              onSelect={setSelectedsKPIs}
              onNext={() => {
                if (selectMultiple) {
                  onSelectMultiple?.(selectedsKPIs);
                  setSelectedsKPIs([]);
                  onClose();
                } else {
                  setCurrentStep(2);
                }
              }}
            />
          </KPIListProvider>
        )}
      </BatchFieldsProvider>
    </ModalV2>
  );
};
