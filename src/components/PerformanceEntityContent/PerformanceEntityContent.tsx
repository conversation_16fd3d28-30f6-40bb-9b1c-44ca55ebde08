import React from 'react';
import { useTranslation } from 'react-i18next';
import { Label } from '..';

type PerformanceEntityContent = {
  performanceEntity: Array<any> | null;
};

export function PerformanceEntityContent({
  performanceEntity,
}: PerformanceEntityContent) {
  const { t } = useTranslation();

  if (!performanceEntity?.length) {
    return (
      <div style={{ color: '#ccc' }}>{t('common_no_performance_entity')}</div>
    );
  }

  return (
    <div style={{ color: '#fff' }}>
      {performanceEntity?.length &&
        performanceEntity.map((item, index) => (
          <div
            style={
              index + 1 < performanceEntity.length
                ? {
                    marginBottom: 4,
                    paddingBottom: 4,
                    borderBottom: '1px solid #ccc',
                  }
                : {}
            }
          >
            <div style={{ fontWeight: 'bold' }}>{item.str_name}</div>
            <Label hexColor="#eee">{t('common_entity_targets')}</Label>
            {item.obj_entities?.map(entity => (
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>
                  <Label hexColor="#ccc" uppercase>
                    {entity.str_name}
                  </Label>
                </div>
                <div>
                  <Label hexColor="#ccc">{`${entity.int_weight}%`}</Label>
                </div>
              </div>
            ))}
          </div>
        ))}
    </div>
  );
}
