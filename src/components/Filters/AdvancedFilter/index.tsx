import {
  Dispatch,
  SetStateAction,
  useCallback,
  useMemo,
  useState,
} from 'react';
import { ButtonV2 } from '@ghq-abi/design-system';
import { useTranslation } from 'react-i18next';
import { TrashIcon } from '@ghq-abi/design-system-icons';
import { useLocation } from 'react-router-dom';
import { useHiddenFiltersContext } from '../../hiddenFilters/HiddenFiltersContext';
import { useUser } from '../../../context/User';
import { getInputsByRoute } from './getInputsByRoute';
import { IInputsProps, renderInputs } from './renderInputs';
import { TargetDocumentListFiltersObjectType } from '../../TargetDocumentList/types';
import { TargetDocumentDropdownFilters } from '../../../services/targetDocument/types';
import * as S from './style';
import ModalV2 from '../../modalV2/ModalV2';

interface IAdvancedFilter {
  handleResetFilters: () => void;
  isResetButtonDisabled: boolean;
  handleApplyFilters: () => void;
  isApplyButtonDisabled: boolean;
  handleInputChange: (name: string, value: any) => void;
  tempFilters: TargetDocumentListFiltersObjectType;
  setTempFilters: Dispatch<SetStateAction<TargetDocumentListFiltersObjectType>>;
  filters: TargetDocumentListFiltersObjectType;
  standardStatuses: string[];
  dataFilters: TargetDocumentDropdownFilters | undefined;
  setDataFilters: Dispatch<
    SetStateAction<TargetDocumentDropdownFilters | undefined>
  >;
}

export const AdvancedFilter = ({
  handleResetFilters,
  isResetButtonDisabled,
  handleApplyFilters,
  isApplyButtonDisabled,
  handleInputChange,
  tempFilters,
  setTempFilters,
  filters,
  standardStatuses,
  dataFilters,
  setDataFilters,
}: IAdvancedFilter) => {
  const { t } = useTranslation();
  const user = useUser();
  const { pathname } = useLocation();

  const { isFiltersVisible, toogleIsVisibleFilters } =
    useHiddenFiltersContext();

  const userHasRole = (roles: string[]) => {
    if (!roles) return true;
    return roles.filter(role => user.user.roles_json.includes(role));
  };

  const ipts = getInputsByRoute(pathname);
  const inputs = ipts.filter((input: IInputsProps) => userHasRole(input.roles));

  const [isCheckSwitch, setIsCheckSwitch] = useState<boolean>(false);

  const handleSwitchChange = (inputName: string) => {
    const nextValue = !isCheckSwitch;
    setIsCheckSwitch(nextValue);
    handleInputChange(inputName, nextValue);
  };

  const processedInputs = useMemo(() => {
    return inputs.map(input => ({
      ...input,
      tempFilters,
      setTempFilters,
      dataFilters,
      setDataFilters,
      standardStatuses,
    }));
  }, [inputs, tempFilters, dataFilters]);

  const memoizedInputs = useCallback(
    (input: IInputsProps) => (
      <S.InputGroup key={input.name}>
        {renderInputs(
          t,
          input,
          handleInputChange,
          handleSwitchChange,
          isCheckSwitch,
        )}
      </S.InputGroup>
    ),
    [t, handleInputChange],
  );

  return (
    <ModalV2
      open={isFiltersVisible}
      title={t('common_title_modal_filters')}
      hasFooter={false}
      onCancel={() => toogleIsVisibleFilters(() => setTempFilters(filters))}
      style={{
        minWidth: '740px',
      }}
    >
      <S.Container>{processedInputs.map(memoizedInputs)}</S.Container>
      <S.ButtonContainer>
        <ButtonV2
          size="md"
          variant="secondary"
          onClick={handleResetFilters}
          disabled={isResetButtonDisabled}
          leftIcon={<TrashIcon />}
        >
          {t('common_reset_filters')}
        </ButtonV2>
        <ButtonV2
          size="md"
          variant="primary"
          onClick={() => {
            handleApplyFilters();
            toogleIsVisibleFilters();
          }}
          disabled={isApplyButtonDisabled}
        >
          {t('common_apply_filters')}
        </ButtonV2>
      </S.ButtonContainer>
    </ModalV2>
  );
};
