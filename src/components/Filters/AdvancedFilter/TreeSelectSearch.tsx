'use client';

import { message, TreeSelect } from 'antd';
import { useState, useCallback } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import performanceEntityService from '../../../services/performanceEntity/performanceEntityService';

const TreeSelectStyled = styled(TreeSelect)`
  .ant-select-tree-list-holder-inner {
    padding-right: 8px;
  }
`;

let fetchTimeout: NodeJS.Timeout;

export default function TreeSelectSearch({ input, value, handleInputChange }) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = useCallback(
    async (itemId?: string, level?: number, searchValue?: string) => {
      setIsLoading(true);
      try {
        const fetcher =
          input.key === 'arr_supervisory_org'
            ? performanceEntityService.getSupervisoryOrgsHierarchy
            : performanceEntityService.getManagementChainHierarchy;

        let result;
        if (searchValue && input.key === 'arr_supervisory_org') {
          result = await performanceEntityService.getSupervisoryOrgsHierarchy(
            searchValue,
          );
        } else {
          result = await fetcher(itemId, level);
        }

        if (result.data && result.data.length) {
          input.setDataFilters(prevFilters => {
            const existingData = prevFilters[input.key] || [];
            let updatedData;

            if (searchValue && input.key === 'arr_supervisory_org') {
              const combinedData = [...result.data];
              updatedData = Array.from(
                new Map(combinedData.map(item => [item.id, item])).values(),
              );
            } else {
              updatedData = [
                ...existingData,
                ...(input.key === 'arr_supervisory_org' && itemId
                  ? result.data.slice(1)
                  : result.data),
              ];

              updatedData = Array.from(
                new Map(updatedData.map(item => [item.id, item])).values(),
              );
            }

            return {
              ...prevFilters,
              [input.key]: updatedData,
            };
          });
        } else if (result.success === false && result.errorMessage) {
          message.error({ content: result.errorMessage, key: 'update' }, 5);
        }
      } catch (error) {
        if (process.env.REACT_APP_STAGE === 'development') {
          console.error('Error fetching data:', error);
        }
      } finally {
        setIsLoading(false);
      }
    },
    [input.key, input.setDataFilters],
  );

  const handleLoadData = (itemTree: any) => {
    return new Promise<void>(resolve => {
      if (itemTree.id === 'SUP_90000578') {
        resolve(undefined);
      } else {
        fetchData(itemTree.id, itemTree.level).then(() => resolve());
      }
    });
  };

  const handleSearch = (searchValue: string, delay = 1000) => {
    clearTimeout(fetchTimeout);
    fetchTimeout = setTimeout(() => {
      fetchData(undefined, undefined, searchValue);
    }, delay);
  };

  const handleClear = async () => {
    handleInputChange(input.name, null);
    const res =
      input.key === 'arr_supervisory_org'
        ? await performanceEntityService.getSupervisoryOrgsHierarchy()
        : await performanceEntityService.getManagementChainHierarchy();

    input.setDataFilters(prevFilters => ({
      ...prevFilters,
      [input.key]: res?.data,
    }));
  };

  return (
    <TreeSelectStyled
      allowClear
      showSearch
      multiple
      maxTagCount={1}
      style={{ width: '100%', height: '40px' }}
      size="large"
      treeDefaultExpandAll={false}
      dropdownStyle={{
        maxHeight: 400,
        overflow: 'auto',
      }}
      value={value}
      placeholder={t(input.placeholder)}
      treeData={input.dataFilters[input.key] || []}
      loadData={handleLoadData}
      getPopupContainer={trigger => trigger.parentNode}
      onClear={handleClear}
      onChange={v => handleInputChange(input.name, v)}
      treeDataSimpleMode
      treeNodeFilterProp="title"
      onSearch={handleSearch}
      onDeselect={handleClear}
      loading={isLoading}
    />
  );
}
