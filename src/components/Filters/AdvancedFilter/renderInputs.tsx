/* eslint-disable react-hooks/rules-of-hooks */
import { Dispatch, SetStateAction } from 'react';
import { Flex, Switch, Skeleton } from '@ghq-abi/design-system';
import { message } from 'antd';
import { TFunction } from 'i18next';
import { TreeSelectStyled } from '../../../modules/MyTeam/Current/styles';
import Search from '../../search/Search';
import { InsertIdsButton } from '../../InsertIdsButton/InsertIdsButton';
import Select from '../../select/Select';
import { StatusFilter } from '../../TargetDocumentList/StatusFilter/StatusFilter';
import { TargetDocumentListFiltersObjectType } from '../../TargetDocumentList/types';
import performanceEntityService from '../../../services/performanceEntity/performanceEntityService';
import * as S from './style';
import TreeSelectSearch from './TreeSelectSearch';

export interface IInputsProps {
  type: string; // name component - for example: Search, TreeSelectStyled...
  name: string; // name input
  label: string; // key translation
  placeholder: string; // key translation
  key: string; // key fetch options (optional)
  roles: string[]; // ['ADMINISTRATOR', 'APPRAISER', ...]
  mode: 'multiple' | 'tags' | undefined;
  tempFilters: Partial<TargetDocumentListFiltersObjectType>;
  setTempFilters: Dispatch<SetStateAction<TargetDocumentListFiltersObjectType>>;
  dataFilters: any;
  setDataFilters: Dispatch<SetStateAction<any>>;
  standardStatuses: string[];
}

export const renderInputs = (
  t: TFunction<'translation', undefined>,
  input: IInputsProps,
  handleInputChange: (name: string, value: any) => void,
  handleSwitchChange: (inputName: string) => void,
  isCheckSwitch: boolean,
) => {
  if (!input.dataFilters)
    return (
      <Skeleton
        key='{"variant":"rectangle","width":100%,"height":40}'
        height={40}
        variant="rectangle"
        width="100%"
      />
    );

  const value = input.tempFilters[input.name] || [];

  const options = input.dataFilters[input.key] || [];

  switch (true) {
    case ['arr_functions'].includes(input.key): {
      const opt =
        input.dataFilters[input.key]?.map(manager => ({
          value: manager.str_function,
          label: manager.str_function?.toUpperCase(),
        })) || [];
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Select
            allowClear
            mode={input.mode}
            maxTagCount={1}
            value={Array.isArray(value) ? value : []}
            placeholder={t(input.placeholder)}
            options={opt}
            getPopupContainer={trigger => trigger.parentNode}
            onChange={v => handleInputChange(input.name, v)}
            onClear={() => handleInputChange(input.name, null)}
          />
        </>
      );
    }
    case ['arr_abi_functions'].includes(input.key): {
      const opt =
        input.dataFilters[input.key]?.map(manager => ({
          value: manager.str_value,
          label: manager.str_value?.toUpperCase(),
        })) || [];
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Select
            allowClear
            mode={input.mode}
            maxTagCount={1}
            value={Array.isArray(value) ? value : []}
            placeholder={t(input.placeholder)}
            options={opt}
            getPopupContainer={trigger => trigger.parentNode}
            onChange={v => handleInputChange(input.name, v)}
            onClear={() => handleInputChange(input.name, null)}
          />
        </>
      );
    }
    case ['arr_supervisory_org', 'arr_management_chain'].includes(input.key): {
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <TreeSelectSearch
            input={input}
            value={value}
            handleInputChange={handleInputChange}
          />
        </>
      );
    }
    case ['arr_managers_lvl_slt', 'arr_managers_lvl_1'].includes(input.key): {
      const opt =
        input.dataFilters[input.key]?.map(manager => ({
          value: manager.str_label,
          label: manager.str_label?.toUpperCase(),
        })) || [];
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Select
            allowClear
            mode={input.mode}
            maxTagCount={1}
            value={Array.isArray(value) ? value : []}
            placeholder={t(input.placeholder)}
            options={opt}
            getPopupContainer={trigger => trigger.parentNode}
            onChange={v => handleInputChange(input.name, v)}
            onClear={() => handleInputChange(input.name, null)}
          />
        </>
      );
    }
    case ['arr_uid_bonus_schema'].includes(input.key): {
      const opt =
        input.dataFilters[input.key]?.map(manager => ({
          value: manager.uid_bonus_schema,
          label: `${manager.str_bonus_plan} - (${manager.str_bonus_schema_zone_name})`,
        })) || [];
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Select
            allowClear
            mode={input.mode}
            maxTagCount={1}
            value={Array.isArray(value) ? value : []}
            placeholder={t(input.placeholder)}
            options={opt}
            getPopupContainer={trigger => trigger.parentNode}
            onChange={v => handleInputChange(input.name, v)}
            onClear={() => handleInputChange(input.name, null)}
          />
        </>
      );
    }
    case ['arr_slt_level'].includes(input.key): {
      const opt =
        input.dataFilters[input.key]?.map(manager => ({
          value: manager.str_slt,
          label: manager.str_slt,
        })) || [];
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Select
            allowClear
            mode={input.mode}
            maxTagCount={1}
            value={Array.isArray(value) ? value : []}
            placeholder={t(input.placeholder)}
            options={opt}
            getPopupContainer={trigger => trigger.parentNode}
            onChange={v => handleInputChange(input.name, v)}
            onClear={() => handleInputChange(input.name, null)}
          />
        </>
      );
    }
    case ['arr_hierarchy'].includes(input.key): {
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <TreeSelectStyled
            allowClear
            showSearch
            multiple
            maxTagCount={1}
            style={{ width: '100%', height: '40px' }}
            size="large"
            treeDefaultExpandAll={false}
            dropdownStyle={{
              maxHeight: 400,
              overflow: 'auto',
            }}
            value={value}
            placeholder={t(input.placeholder)}
            treeData={options}
            getPopupContainer={trigger => trigger.parentNode}
            onClear={() => handleInputChange(input.name, null)}
            onChange={v => handleInputChange(input.name, v)}
            treeDataSimpleMode
            treeNodeFilterProp="title"
          />
        </>
      );
    }
    case input.type === 'N1':
      return (
        <S.N1Container>
          <Switch
            name={input.name}
            value={value}
            onClick={() => handleSwitchChange(input.name)}
            checked={isCheckSwitch}
          />{' '}
          <span>N-1 Structure</span>
        </S.N1Container>
      );
    case input.type === 'Search/Nine':
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Flex gap="sm">
            <Search
              name={input.name}
              placeholder={t(input.placeholder)}
              wrapperStyles={{ width: '100%' }}
              value={value}
              onChange={e =>
                handleInputChange(input.name, e.currentTarget.value)
              }
            />
            <InsertIdsButton
              searchValue={`${input.tempFilters.str_manager_name_id}`}
              onUpdateSearch={newValue =>
                input.setTempFilters(prev => ({
                  ...prev,
                  str_manager_name_id: newValue,
                }))
              }
              key={input.tempFilters.str_employee_name_id}
            />
          </Flex>
        </>
      );
    case input.type === 'Search':
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Search
            name={input.name}
            placeholder={t(input.placeholder)}
            wrapperStyles={{ width: '100%' }}
            value={value}
            onChange={e => handleInputChange(input.name, e.currentTarget.value)}
          />
        </>
      );
    case input.type === 'Select':
      return (
        <>
          <S.InputLabel>{t(input.label)}</S.InputLabel>
          <Select
            allowClear
            mode={input.mode}
            maxTagCount={1}
            value={Array.isArray(value) ? value : []}
            placeholder={t(input.placeholder)}
            options={options}
            getPopupContainer={trigger => trigger.parentNode}
            onChange={v => handleInputChange(input.name, v)}
            onClear={() => handleInputChange(input.name, null)}
          />
        </>
      );
    case input.type === 'StatusFilter':
      return (
        <StatusFilter
          tempFilters={input.tempFilters}
          setTempFilters={input.setTempFilters}
          standardStatuses={input.standardStatuses}
        />
      );
    default:
      return null;
  }
};
