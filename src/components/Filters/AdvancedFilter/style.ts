import styled from 'styled-components';
import { FilterLabel } from '../../filterLabel';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  width: 688px;
  max-height: 465px;

  background-color: #ffffff;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #eeeff2;
  gap: 8px;
  overflow-x: hidden;
  overflow-y: auto;
`;

export const DialogHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 19px 0;

  svg {
    > path {
      fill: #99b2c6;
    }
    cursor: pointer;
  }
`;

export const DialogTitle = styled.div`
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
`;
export const DialogIcon = styled.div`
  display: flex;
  align-items: end;
`;

export const N1Container = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;

  span {
    font-size: 10px;
    color: #3f465a;
    font-weight: 600;
  }
`;

export const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
`;
export const InputLabel = styled(FilterLabel)`
  color: #7d8597;
  font-size: 12px;
`;

export const ButtonContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 22px;
`;
