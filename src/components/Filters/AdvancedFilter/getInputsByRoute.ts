export const getInputsByRoute = (pathname: string) => {
  const routesConfig = {
    '/my-team/cascading': [
      {
        type: 'N1',
        name: 'array_display',
      },
      {
        type: 'Search/Nine',
        name: 'str_employee_position',
        label: 'common_position',
        placeholder: 'common_search_by_position',
      },
      {
        type: 'StatusFilter',
        name: 'arr_status',
        label: 'common_status',
        placeholder: 'common_select_option',
      },
    ],
    '/my-team/current': [
      {
        type: 'N1',
        name: 'array_display',
      },
      {
        type: 'Search/Nine',
        name: 'str_employee_position',
        label: 'common_position',
        placeholder: 'common_search_by_position',
      },
      {
        type: 'StatusFilter',
        name: 'arr_status',
        label: 'common_status',
        placeholder: 'common_select_option',
      },
    ],
    '/my-scope/cascading': [
      {
        type: 'Search/Nine',
        name: 'str_manager_name_id',
        label: 'common_appraiser',
        placeholder: 'common_search_appraiser_name_id',
      },
      {
        type: '<PERSON><PERSON>ilter',
        name: 'arr_status',
        label: 'common_status',
        placeholder: 'common_select_option',
        key: 'arr_status',
      },
      {
        type: 'Select',
        name: 'str_zone_id',
        label: 'common_zone',
        placeholder: 'common_select_option',
        key: 'arr_zones',
        mode: 'multiple',
        roles: ['ADMINISTRATOR'],
      },
      {
        type: 'Select',
        name: 'arr_managers_lvl_slt',
        label: 'common_manager_lvl_slt',
        placeholder: 'common_select_option',
        key: 'arr_managers_lvl_slt',
      },
      {
        type: 'Select',
        name: 'arr_managers_lvl_1',
        label: 'common_manager_lvl_1',
        placeholder: 'common_select_option',
        key: 'arr_managers_lvl_1',
      },
      {
        type: 'Select',
        name: 'str_function_abi_entity',
        label: 'common_function_abi_entity',
        placeholder: 'common_select_option',
        key: 'arr_abi_functions',
      },
      {
        type: 'Search',
        name: 'str_employee_position',
        label: 'common_position',
        placeholder: 'common_search_by_position',
      },
      {
        type: 'Select',
        name: 'str_function',
        label: 'common_function',
        placeholder: 'common_select_option',
        key: 'arr_functions',
      },
      {
        type: 'Select',
        name: 'arr_slt_level',
        label: 'common_slt_level',
        placeholder: 'common_select_option',
        key: 'arr_slt_level',
      },
      {
        type: 'Select',
        name: 'uid_bonus_schema',
        label: 'common_bonus_plan',
        placeholder: 'common_select_option',
        key: 'arr_uid_bonus_schema',
      },
      {
        type: 'TreeSelectStyled',
        name: 'arr_macroentity',
        label: 'common_macro_entity',
        placeholder: 'common_select_option',
        key: 'arr_hierarchy',
        roles: ['ADMINISTRATOR', 'TSC', 'TSCSPOCPPM'],
      },
      {
        type: 'TreeSelectStyled',
        name: 'arr_supervisory_org',
        label: 'common_supervisory_org',
        placeholder: 'common_select_option',
        key: 'arr_supervisory_org',
      },
    ],
    '/my-scope/trackmonitoring': [
      {
        type: 'Search/Nine',
        name: 'str_manager_name_id',
        label: 'common_appraiser',
        placeholder: 'common_search_appraiser_name_id',
      },
      {
        type: 'StatusFilter',
        name: 'arr_status',
        label: 'common_status',
        placeholder: 'common_select_option',
      },
      {
        type: 'Select',
        name: 'str_zone_id',
        label: 'common_zone',
        placeholder: 'common_select_option',
        key: 'arr_zones',
        mode: 'multiple',
        roles: ['ADMINISTRATOR'],
      },
      {
        type: 'Select',
        name: 'arr_managers_lvl_slt',
        label: 'common_manager_lvl_slt',
        placeholder: 'common_select_option',
        key: 'arr_managers_lvl_slt',
      },
      {
        type: 'Select',
        name: 'arr_managers_lvl_1',
        label: 'common_manager_lvl_1',
        placeholder: 'common_select_option',
        key: 'arr_managers_lvl_1',
      },
      {
        type: 'Select',
        name: 'str_function_abi_entity',
        label: 'common_function_abi_entity',
        placeholder: 'common_select_option',
        key: 'arr_abi_functions',
      },
      {
        type: 'Search',
        name: 'str_employee_position',
        label: 'common_position',
        placeholder: 'common_search_by_position',
      },
      {
        type: 'Select',
        name: 'str_function',
        label: 'common_function',
        placeholder: 'common_select_option',
        key: 'arr_functions',
      },
      {
        type: 'Select',
        name: 'arr_slt_level',
        label: 'common_slt_level',
        placeholder: 'common_select_option',
        key: 'arr_slt_level',
      },
      {
        type: 'Select',
        name: 'uid_bonus_schema',
        label: 'common_bonus_plan',
        placeholder: 'common_select_option',
        key: 'arr_uid_bonus_schema',
      },
      {
        type: 'TreeSelectStyled',
        name: 'arr_macroentity',
        label: 'common_macro_entity',
        placeholder: 'common_select_option',
        key: 'arr_hierarchy',
        roles: ['ADMINISTRATOR', 'TSC', 'TSCSPOCPPM'],
      },
      {
        type: 'TreeSelectStyled',
        name: 'arr_supervisory_org',
        label: 'common_supervisory_org',
        placeholder: 'common_select_option',
        key: 'arr_supervisory_org',
      },
      {
        type: 'Search',
        name: 'str_org_name_id',
        label: 'common_org_id',
        placeholder: 'common_search',
      },
    ],
    '/my-scope/appraisal': [
      {
        type: 'Search/Nine',
        name: 'str_manager_name_id',
        label: 'common_appraiser',
        placeholder: 'common_search_appraiser_name_id',
      },
      {
        type: 'StatusFilter',
        name: 'arr_status',
        label: 'common_status',
        placeholder: 'common_select_option',
      },
      {
        type: 'Select',
        name: 'str_zone_id',
        label: 'common_zone',
        placeholder: 'common_select_option',
        key: 'arr_zones',
        mode: 'multiple',
        roles: ['ADMINISTRATOR'],
      },
      {
        type: 'Select',
        name: 'arr_managers_lvl_slt',
        label: 'common_manager_lvl_slt',
        placeholder: 'common_select_option',
        key: 'arr_managers_lvl_slt',
      },
      {
        type: 'Select',
        name: 'arr_managers_lvl_1',
        label: 'common_manager_lvl_1',
        placeholder: 'common_select_option',
        key: 'arr_managers_lvl_1',
      },
      {
        type: 'Select',
        name: 'str_function_abi_entity',
        label: 'common_function_abi_entity',
        placeholder: 'common_select_option',
        key: 'arr_abi_functions',
      },
      {
        type: 'Search',
        name: 'str_employee_position',
        label: 'common_position',
        placeholder: 'common_search_by_position',
      },
      {
        type: 'Select',
        name: 'str_function',
        label: 'common_function',
        placeholder: 'common_select_option',
        key: 'arr_functions',
      },
      {
        type: 'Select',
        name: 'arr_slt_level',
        label: 'common_slt_level',
        placeholder: 'common_select_option',
        key: 'arr_slt_level',
      },
      {
        type: 'Select',
        name: 'uid_bonus_schema',
        label: 'common_bonus_plan',
        placeholder: 'common_select_option',
        key: 'arr_uid_bonus_schema',
      },
      {
        type: 'TreeSelectStyled',
        name: 'arr_macroentity',
        label: 'common_macro_entity',
        placeholder: 'common_select_option',
        key: 'arr_hierarchy',
        roles: ['ADMINISTRATOR', 'TSC', 'TSCSPOCPPM'],
      },
      {
        type: 'TreeSelectStyled',
        name: 'arr_supervisory_org',
        label: 'common_supervisory_org',
        placeholder: 'common_select_option',
        key: 'arr_supervisory_org',
      },
      {
        type: 'Search',
        name: 'str_org_name_id',
        label: 'common_org_id',
        placeholder: 'common_search',
      },
    ],
  };
  return routesConfig[pathname] || [];
};
