import languages from '../../libs/i18n/languages';

declare module 'i18next' {
  interface CustomTypeOptions {
    // The type for resources had to be removed because it was causing extreme
    // slowdown to TypeScript checking and autocomplete
    // resources: typeof languages;
    // If you see an error like: "Argument of type 'DefaultTFuncReturn' is not
    // assignable to parameter of type xyz" set returnNull to false (and also
    // in the i18next init options).
    returnNull: false;
  }
}
