import { DISABLED_PA_VALUES } from '../constants';
import { KpiTypeUuids } from '../utils';

export function isPAValueDisabled(kpi: {
  uid_type?: string;
  bol_kpi_package?: number | boolean;
}) {
  return DISABLED_PA_VALUES.includes(kpi.uid_type!) || !!kpi.bol_kpi_package;
}

export function getPaValueByValueAndKpiType(
  str_value: string,
  type: KpiTypeUuids,
  pa_value?: string,
) {
  if (type === KpiTypeUuids.DASHBOARD) {
    return str_value === '4'
      ? '3/4 is 80%, miss more than 1 then 0%'
      : str_value >= '5' && str_value <= '8'
      ? 'Miss 1: 90%, Miss 2: 80%, Miss more than 2: 0%'
      : 'N/A';
  }
  if (type === KpiTypeUuids.MILESTONE) {
    return str_value === '4' ? '3/4 is 80%, miss more than 1 then 0%' : 'N/A';
  }
  if (type === KpiTypeUuids.YES_NO) {
    return 'N/A';
  }
  return pa_value;
}
