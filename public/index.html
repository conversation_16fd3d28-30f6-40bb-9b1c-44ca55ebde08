<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <!-- TODO: REMOVE THIS CASH FOR THE FUTURE, THIS IS TEMPORARY SOLUTION -->
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <!-- TODO: REMOVE THIS CASH FOR THE FUTURE, THIS IS TEMPORARY SOLUTION -->

    <title>Northstar</title>
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@200;300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="icon" type="image/png" href="./files/thumbnail.png" />
  </head>
  <body>
    <div id="root"></div>
    <script type="text/javascript" src="VAR_METRICS_URL"></script>
    <script id="plugin" type="text/javascript">
      var metricsPlugin;
      setTimeout(() => {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (metricsPlugin && metricsPlugin.plugin && user)
          metricsPlugin.plugin(user.int_employeeglobalid, "NorthStar", user.str_preferredlanguage);
      }, 2000);
    </script>
    <script async src="https://www.googletagmanager.com/gtag/js?id=VAR_GA_TAG"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'VAR_GA_TAG');
      window.gtag = gtag;
    </script>
  </body>

</html>
